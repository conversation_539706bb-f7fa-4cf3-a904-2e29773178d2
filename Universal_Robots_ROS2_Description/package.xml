<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>ur_description</name>
  <version>2.6.0</version>
  <description>
    URDF description for Universal Robots
  </description>

  <author><PERSON></author>
  <author><PERSON></author>
  <author>G.A. vd. <PERSON></author>
  <author><PERSON><PERSON><PERSON></author>
  <author><PERSON></author>
  <author><PERSON><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Rune Søe-Knudsen</maintainer>
  <maintainer email="<EMAIL>">Universal Robots A/S</maintainer>

  <license file="LICENSE">BSD-3-Clause</license>
  <!-- The UR15, UR20 and UR30 meshes constitutes “Graphical Documentation” the use of which is subject to and governed by our “Terms and Conditions for use of Graphical Documentation”, which can be found here: https://www.universal-robots.com/legal/terms-and-conditions/terms_and_conditions_for_use_of_graphical_documentation.txt -->
  <license file="meshes/ur20/LICENSE.txt">Universal Robots A/S’ Terms and Conditions for Use of Graphical Documentation</license>
  <!--<license file="meshes/ur15/LICENSE.txt" source-files="meshes/ur15/*" type="freeform">Universal Robots A/S’ Terms and Conditions for Use of Graphical Documentation</license>-->
  <!--<license file="meshes/ur20/LICENSE.txt" source-files="meshes/ur20/*" type="freeform">Universal Robots A/S’ Terms and Conditions for Use of Graphical Documentation</license>-->
  <!--<license file="meshes/ur30/LICENSE.txt" source-files="meshes/ur30/*" type="freeform">Universal Robots A/S’ Terms and Conditions for Use of Graphical Documentation</license>-->

  <url type="bugtracker">https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/issues</url>
  <url type="repository">https://github.com/UniversalRobots/Universal_Robots_ROS2_Description</url>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>joint_state_publisher_gui</exec_depend>
  <exec_depend>launch</exec_depend>
  <exec_depend>launch_ros</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>rviz2</exec_depend>
  <exec_depend>urdf</exec_depend>
  <exec_depend>xacro</exec_depend>

  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>liburdfdom-tools</test_depend>
  <test_depend>xacro</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
