## Build Status

This page gives a detailed overview of the build status of this repository. Please note that due to
upstream changes some pipelines might turn red temporarily which can be expected behavior.

<table width="100%">
  <tr>
    <th></th>
    <th>Humble</th>
    <th>Jazzy</th>
    <th>Kilted</th>
    <th>Rolling</th>
  </tr>
  <tr>
    <th>Branch</th>
    <td><a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/tree/humble">humble</a></td>
    <td><a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/tree/jazzy">jazzy</a></td>
    <td><a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/tree/rolling">rolling</a></td>
    <td><a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/tree/rolling">rolling</a></td>
  </tr>
  <tr>
    <th><PERSON><PERSON> builds</th>
    <td>
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/humble-binary-main.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/humble-binary-main.yml/badge.svg?event=schedule"
              alt="Humble Binary Main"/>
      </a> <br />
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/humble-binary-testing.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/humble-binary-testing.yml/badge.svg?event=schedule"
              alt="Humble Binary Testing"/>
      </a> <br />
    </td>
    <td>
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/jazzy-binary-main.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/jazzy-binary-main.yml/badge.svg?event=schedule"
              alt="Jazzy Binary Main"/>
      </a> <br />
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/jazzy-binary-testing.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/jazzy-binary-testing.yml/badge.svg?event=schedule"
              alt="Jazzy Binary Testing"/>
      </a> <br />
    </td>
    <td> <!-- Kilted -->
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/kilted-binary-main.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/kilted-binary-main.yml/badge.svg?event=schedule"
              alt="Kilted Binary Main"/>
      </a> <br />
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/kilted-binary-testing.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/kilted-binary-testing.yml/badge.svg?event=schedule"
              alt="Kilted Binary Testing"/>
      </a> <br />
    </td>
    <td>
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/rolling-binary-main.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/rolling-binary-main.yml/badge.svg?event=schedule"
              alt="Rolling Binary Main"/>
      </a> <br />
      <a href="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/rolling-binary-testing.yml?query=event%3Aschedule++">
         <img src="https://github.com/UniversalRobots/Universal_Robots_ROS2_Description/actions/workflows/rolling-binary-testing.yml/badge.svg?event=schedule"
              alt="Rolling Binary Testing"/>
      </a> <br />
    </td>
  </tr>
  <tr>
    <th>Buildfarm</th>
    <td>
      <a href='https://build.ros2.org/job/Hbin_uJ64__ur_description__ubuntu_jammy_amd64__binary/'><img src='https://build.ros2.org/job/Hbin_uJ64__ur_description__ubuntu_jammy_amd64__binary/badge/icon?subject=uJ64_ur_description'></a>
    </td>
    <td>
      <a href='https://build.ros2.org/job/Jbin_uN64__ur_description__ubuntu_noble_amd64__binary/'><img src='https://build.ros2.org/job/Jbin_uN64__ur_description__ubuntu_noble_amd64__binary/badge/icon?subject=uN64_ur_description'></a>
    </td>
    <td>
      <a href='https://build.ros2.org/job/Kbin_uN64__ur_description__ubuntu_noble_amd64__binary/'><img src='https://build.ros2.org/job/Kbin_uN64__ur_description__ubuntu_noble_amd64__binary/badge/icon?subject=uN64_ur_description'></a>
    </td>
    <td>
      <a href='https://build.ros2.org/job/Rbin_uN64__ur_description__ubuntu_noble_amd64__binary/'><img src='https://build.ros2.org/job/Rbin_uN64__ur_description__ubuntu_noble_amd64__binary/badge/icon?subject=uN64_ur_description'></a>
    </td>
  </tr>
</table>
