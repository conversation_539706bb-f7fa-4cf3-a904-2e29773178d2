mesh_files:
  base:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/base.dae
      material:
        name: "Light<PERSON>rey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/base.stl

  shoulder:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/shoulder.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/shoulder.stl

  upper_arm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/upperarm.dae
      material:
        name: "Light<PERSON>rey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/upperarm.stl
      mesh_files:

  forearm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/forearm.dae
      material:
        name: "Light<PERSON>rey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/forearm.stl

  wrist_1:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/wrist1.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/wrist1.stl
    visual_offset: -0.104

  wrist_2:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/wrist2.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/wrist2.stl
    visual_offset: -0.08535

  wrist_3:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3e/visual/wrist3.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3e/collision/wrist3.stl
    visual_offset: -0.0921
