<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro">
  <!--
    Base UR robot series xacro macro.

    NOTE this is NOT a URDF. It cannot directly be loaded by consumers
    expecting a flattened '.urdf' file. See the top-level '.xacro' for that
    (but note that .xacro must still be processed by the xacro command).

    This file models the base kinematic chain of a UR robot, which then gets
    parameterised by various configuration files to convert it into a UR3(e),
    UR5(e), UR10(e) or UR16e.

    NOTE the default kinematic parameters (i.e., link lengths, frame locations,
    offsets, etc) do not correspond to any particular robot. They are defaults
    only. There WILL be non-zero offsets between the Forward Kinematics results
    in TF (i.e., robot_state_publisher) and the values reported by the Teach
    Pendant.

    For accurate (and robot-specific) transforms, the 'kinematics_parameters_file'
    parameter MUST point to a .yaml file containing the appropriate values for
    the targeted robot.

    If using the UniversalRobots/Universal_Robots_ROS_Driver, follow the steps
    described in the readme of that repository to extract the kinematic
    calibration from the controller and generate the required .yaml file.

    Main author of the migration to yaml configs <PERSON><PERSON><PERSON>.

    Contributors to previous versions (in no particular order)

     - <PERSON>l
     - <PERSON>vro <PERSON>ov
     - <PERSON>
     - <PERSON>
     - Wim <PERSON>
     - <PERSON>
     - <PERSON><PERSON>h <PERSON>
     - <PERSON>sh<PERSON>
     - G. vd. Ho<PERSON>
     - <PERSON> <PERSON>
     - <PERSON>
     - <PERSON> P<PERSON>
     - <PERSON>edtke
     - <PERSON> <PERSON>irring
     - <PERSON> <PERSON> Dr<PERSON>ki
     - <PERSON> Exner
     - <PERSON>
     - <PERSON>shna N L
     - <PERSON> Asif <PERSON>
  -->

  <xacro:include filename="$(find ur_description)/urdf/inc/ur_transmissions.xacro" />
  <xacro:include filename="$(find ur_description)/urdf/inc/ur_common.xacro" />

  <xacro:macro name="ur_robot" params="
    name
    tf_prefix
    parent
    *origin
    joint_limits_parameters_file
    kinematics_parameters_file
    physical_parameters_file
    visual_parameters_file
    generate_ros2_control_tag:=true
    transmission_hw_interface:=hardware_interface/PositionJointInterface
    safety_limits:=false
    safety_pos_margin:=0.15
    safety_k_position:=20
    use_fake_hardware:=false
    fake_sensor_commands:=false
    sim_gazebo:=false
    sim_ignition:=false
    headless_mode:=false
    initial_positions:=${dict(shoulder_pan_joint=0.0,shoulder_lift_joint=-1.57,elbow_joint=0.0,wrist_1_joint=-1.57,wrist_2_joint=0.0,wrist_3_joint=0.0)}
    use_tool_communication:=false
    tool_voltage:=0
    tool_parity:=0
    tool_baud_rate:=115200
    tool_stop_bits:=1
    tool_rx_idle_chars:=1.5
    tool_tx_idle_chars:=3.5
    tool_device_name:=/tmp/ttyUR
    tool_tcp_port:=54321
    robot_ip:=0.0.0.0
    script_filename:=to_be_filled_by_ur_robot_driver
    output_recipe_filename:=to_be_filled_by_ur_robot_driver
    input_recipe_filename:=to_be_filled_by_ur_robot_driver
    reverse_port:=50001
    script_sender_port:=50002
    reverse_ip:=0.0.0.0
    script_command_port:=50004
    trajectory_port:=50003
    non_blocking_read:=true
    keep_alive_count:=2"

    >

    <!-- Load configuration data from the provided .yaml files -->
    <xacro:read_model_data
      joint_limits_parameters_file="${joint_limits_parameters_file}"
      kinematics_parameters_file="${kinematics_parameters_file}"
      physical_parameters_file="${physical_parameters_file}"
      visual_parameters_file="${visual_parameters_file}"
      force_abs_paths="${sim_gazebo or sim_ignition}"/>


    <xacro:if value="${generate_ros2_control_tag}">
      <!-- ros2 control include -->
      <xacro:include filename="$(find ur_description)/urdf/ur.ros2_control.xacro" />
      <!-- ros2 control instance -->
      <xacro:ur_ros2_control
        name="${name}"
        use_fake_hardware="${use_fake_hardware}"
        initial_positions="${initial_positions}"
        fake_sensor_commands="${fake_sensor_commands}"
        headless_mode="${headless_mode}"
        sim_gazebo="${sim_gazebo}"
        sim_ignition="${sim_ignition}"
        script_filename="${script_filename}"
        output_recipe_filename="${output_recipe_filename}"
        input_recipe_filename="${input_recipe_filename}"
        tf_prefix="${tf_prefix}"
        hash_kinematics="${kinematics_hash}"
        robot_ip="${robot_ip}"
        use_tool_communication="${use_tool_communication}"
        tool_voltage="${tool_voltage}"
        tool_parity="${tool_parity}"
        tool_baud_rate="${tool_baud_rate}"
        tool_stop_bits="${tool_stop_bits}"
        tool_rx_idle_chars="${tool_rx_idle_chars}"
        tool_tx_idle_chars="${tool_tx_idle_chars}"
        tool_device_name="${tool_device_name}"
        tool_tcp_port="${tool_tcp_port}"
        reverse_port="${reverse_port}"
        script_sender_port="${script_sender_port}"
        reverse_ip="${reverse_ip}"
        script_command_port="${script_command_port}"
        trajectory_port="${trajectory_port}"
        non_blocking_read="${non_blocking_read}"
        keep_alive_count="${keep_alive_count}"
        />
    </xacro:if>

    <!-- Add URDF transmission elements (for ros_control) -->
    <!--<xacro:ur_arm_transmission prefix="${prefix}" hw_interface="${transmission_hw_interface}" />-->
    <!-- Placeholder for ros2_control transmission which don't yet exist -->

    <!-- links -  main serial chain -->
    <link name="${tf_prefix}base_link"/>
    <link name="${tf_prefix}base_link_inertia">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="base" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="base" type="collision"/>
        </geometry>
      </collision>
      <xacro:cylinder_inertial radius="${base_inertia_radius}" length="${base_inertia_length}" mass="${base_mass}">
        <origin xyz="0 0 0" rpy="0 0 0" />
      </xacro:cylinder_inertial>
    </link>
    <link name="${tf_prefix}shoulder_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="shoulder" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="shoulder" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${shoulder_mass}"/>
        <origin rpy="${shoulder_inertia_rotation}" xyz="${shoulder_cog}"/>
        <inertia
            ixx="${shoulder_inertia_ixx}"
            ixy="${shoulder_inertia_ixy}"
            ixz="${shoulder_inertia_ixz}"
            iyy="${shoulder_inertia_iyy}"
            iyz="${shoulder_inertia_iyz}"
            izz="${shoulder_inertia_izz}"
        />
      </inertial>    </link>
    <link name="${tf_prefix}upper_arm_link">
      <visual>
        <origin xyz="0 0 ${shoulder_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="upper_arm" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${shoulder_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="upper_arm" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${upper_arm_mass}"/>
        <origin rpy="${upper_arm_inertia_rotation}" xyz="${upper_arm_cog}"/>
        <inertia
            ixx="${upper_arm_inertia_ixx}"
            ixy="${upper_arm_inertia_ixy}"
            ixz="${upper_arm_inertia_ixz}"
            iyy="${upper_arm_inertia_iyy}"
            iyz="${upper_arm_inertia_iyz}"
            izz="${upper_arm_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}forearm_link">
      <visual>
        <origin xyz="0 0 ${elbow_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="forearm" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${elbow_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="forearm" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${forearm_mass}"/>
        <origin rpy="${forearm_inertia_rotation}" xyz="${forearm_cog}"/>
        <inertia
            ixx="${forearm_inertia_ixx}"
            ixy="${forearm_inertia_ixy}"
            ixz="${forearm_inertia_ixz}"
            iyy="${forearm_inertia_iyy}"
            iyz="${forearm_inertia_iyz}"
            izz="${forearm_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_1_link">
	  <xacro:get_visual_params name="wrist_1" type="visual_offset"/>
      <visual>
      	<origin xyz="0 0 ${visual_params}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_1" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${visual_params}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_1" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_1_mass}"/>
        <origin rpy="${wrist_1_inertia_rotation}" xyz="${wrist_1_cog}"/>
        <inertia
            ixx="${wrist_1_inertia_ixx}"
            ixy="${wrist_1_inertia_ixy}"
            ixz="${wrist_1_inertia_ixz}"
            iyy="${wrist_1_inertia_iyy}"
            iyz="${wrist_1_inertia_iyz}"
            izz="${wrist_1_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_2_link">
          <xacro:get_visual_params name="wrist_2" type="visual_offset"/>
      <visual>
        <origin xyz="0 0 ${visual_params}" rpy="0 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_2" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${visual_params}" rpy="0 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_2" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_2_mass}"/>
        <origin rpy="${wrist_2_inertia_rotation}" xyz="${wrist_2_cog}"/>
        <inertia
            ixx="${wrist_2_inertia_ixx}"
            ixy="${wrist_2_inertia_ixy}"
            ixz="${wrist_2_inertia_ixz}"
            iyy="${wrist_2_inertia_iyy}"
            iyz="${wrist_2_inertia_iyz}"
            izz="${wrist_2_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_3_link">
      <!-- TODO: remove this once all wrist_3 meshes have the visual_offset_xyz tag -->
      <xacro:get_visual_params name="wrist_3" type="visual_offset_xyz"/>
      <xacro:property name="mesh_offset" value="${visual_params}" scope="parent"/>

      <xacro:if value="${visual_params == ''}">
        <xacro:get_visual_params name="wrist_3" type="visual_offset"/>
        <xacro:property name="mesh_offset" value="0 0 ${visual_params}" scope="parent"/>
      </xacro:if>
      <visual>
        <origin xyz="${mesh_offset}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_3" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="${mesh_offset}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_3" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_3_mass}"/>
        <origin rpy="${wrist_3_inertia_rotation}" xyz="${wrist_3_cog}"/>
        <inertia
            ixx="${wrist_3_inertia_ixx}"
            ixy="${wrist_3_inertia_ixy}"
            ixz="${wrist_3_inertia_ixz}"
            iyy="${wrist_3_inertia_iyy}"
            iyz="${wrist_3_inertia_iyz}"
            izz="${wrist_3_inertia_izz}"
        />
      </inertial>
    </link>

    <!-- base_joint fixes base_link to the environment -->
    <joint name="${tf_prefix}base_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent}" />
      <child link="${tf_prefix}base_link" />
    </joint>

    <!-- joints - main serial chain -->
    <joint name="${tf_prefix}base_link-base_link_inertia" type="fixed">
      <parent link="${tf_prefix}base_link" />
      <child link="${tf_prefix}base_link_inertia" />
      <!-- 'base_link' is REP-103 aligned (so X+ forward), while the internal
           frames of the robot/controller have X+ pointing backwards.
           Use the joint between 'base_link' and 'base_link_inertia' (a dummy
           link/frame) to introduce the necessary rotation over Z (of pi rad).
      -->
      <origin xyz="0 0 0" rpy="0 0 ${pi}" />
    </joint>
    <joint name="${tf_prefix}shoulder_pan_joint" type="revolute">
      <parent link="${tf_prefix}base_link_inertia" />
      <child link="${tf_prefix}shoulder_link" />
      <origin xyz="${shoulder_x} ${shoulder_y} ${shoulder_z}" rpy="${shoulder_roll} ${shoulder_pitch} ${shoulder_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${shoulder_pan_lower_limit}" upper="${shoulder_pan_upper_limit}"
        effort="${shoulder_pan_effort_limit}" velocity="${shoulder_pan_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${shoulder_pan_lower_limit + safety_pos_margin}" soft_upper_limit="${shoulder_pan_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}shoulder_lift_joint" type="revolute">
      <parent link="${tf_prefix}shoulder_link" />
      <child link="${tf_prefix}upper_arm_link" />
      <origin xyz="${upper_arm_x} ${upper_arm_y} ${upper_arm_z}" rpy="${upper_arm_roll} ${upper_arm_pitch} ${upper_arm_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${shoulder_lift_lower_limit}" upper="${shoulder_lift_upper_limit}"
        effort="${shoulder_lift_effort_limit}" velocity="${shoulder_lift_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${shoulder_lift_lower_limit + safety_pos_margin}" soft_upper_limit="${shoulder_lift_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}elbow_joint" type="revolute">
      <parent link="${tf_prefix}upper_arm_link" />
      <child link="${tf_prefix}forearm_link" />
      <origin xyz="${forearm_x} ${forearm_y} ${forearm_z}" rpy="${forearm_roll} ${forearm_pitch} ${forearm_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${elbow_joint_lower_limit}" upper="${elbow_joint_upper_limit}"
        effort="${elbow_joint_effort_limit}" velocity="${elbow_joint_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${elbow_joint_lower_limit + safety_pos_margin}" soft_upper_limit="${elbow_joint_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_1_joint" type="revolute">
      <parent link="${tf_prefix}forearm_link" />
      <child link="${tf_prefix}wrist_1_link" />
      <origin xyz="${wrist_1_x} ${wrist_1_y} ${wrist_1_z}" rpy="${wrist_1_roll} ${wrist_1_pitch} ${wrist_1_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${wrist_1_lower_limit}" upper="${wrist_1_upper_limit}"
        effort="${wrist_1_effort_limit}" velocity="${wrist_1_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${wrist_1_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_1_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_2_joint" type="revolute">
      <parent link="${tf_prefix}wrist_1_link" />
      <child link="${tf_prefix}wrist_2_link" />
      <origin xyz="${wrist_2_x} ${wrist_2_y} ${wrist_2_z}" rpy="${wrist_2_roll} ${wrist_2_pitch} ${wrist_2_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${wrist_2_lower_limit}" upper="${wrist_2_upper_limit}"
             effort="${wrist_2_effort_limit}" velocity="${wrist_2_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${wrist_2_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_2_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_3_joint" type="${wrist_3_joint_type}">
      <parent link="${tf_prefix}wrist_2_link" />
      <child link="${tf_prefix}wrist_3_link" />
      <origin xyz="${wrist_3_x} ${wrist_3_y} ${wrist_3_z}" rpy="${wrist_3_roll} ${wrist_3_pitch} ${wrist_3_yaw}" />
      <axis xyz="0 0 1" />
      <xacro:if value="${wrist_3_joint_type != 'continuous'}">
        <limit lower="${wrist_3_lower_limit}" upper="${wrist_3_upper_limit}"
          effort="${wrist_3_effort_limit}" velocity="${wrist_3_velocity_limit}"/>
        <xacro:if value="${safety_limits}">
          <safety_controller soft_lower_limit="${wrist_3_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_3_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
        </xacro:if>
      </xacro:if>
      <xacro:unless value="${wrist_3_joint_type != 'continuous'}">
        <limit effort="${wrist_3_effort_limit}" velocity="${wrist_3_velocity_limit}"/>
        <xacro:if value="${safety_limits}">
          <safety_controller k_position="${safety_k_position}" k_velocity="0.0"/>
        </xacro:if>
      </xacro:unless>
      <dynamics damping="0" friction="0"/>
    </joint>

    <link name="${tf_prefix}ft_frame"/>
    <joint name="${tf_prefix}wrist_3_link-ft_frame" type="fixed">
      <parent link="${tf_prefix}wrist_3_link"/>
      <child link="${tf_prefix}ft_frame"/>
      <origin xyz="0 0 0" rpy="${pi} 0 0"/>
    </joint>

    <!-- ROS-Industrial 'base' frame - base_link to UR 'Base' Coordinates transform -->
    <link name="${tf_prefix}base"/>
    <joint name="${tf_prefix}base_link-base_fixed_joint" type="fixed">
      <!-- Note the rotation over Z of pi radians - as base_link is REP-103
           aligned (i.e., has X+ forward, Y+ left and Z+ up), this is needed
           to correctly align 'base' with the 'Base' coordinate system of
           the UR controller.
      -->
      <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
      <parent link="${tf_prefix}base_link"/>
      <child link="${tf_prefix}base"/>
    </joint>

    <!-- ROS-Industrial 'flange' frame - attachment point for EEF models -->
    <link name="${tf_prefix}flange" />
    <joint name="${tf_prefix}wrist_3-flange" type="fixed">
      <parent link="${tf_prefix}wrist_3_link" />
      <child link="${tf_prefix}flange" />
      <origin xyz="0 0 0" rpy="0 ${-pi/2.0} ${-pi/2.0}" />
    </joint>

    <!-- ROS-Industrial 'tool0' frame - all-zeros tool frame -->
    <link name="${tf_prefix}tool0"/>
    <joint name="${tf_prefix}flange-tool0" type="fixed">
      <!-- default toolframe - X+ left, Y+ up, Z+ front -->
      <origin xyz="0 0 0" rpy="${pi/2.0} 0 ${pi/2.0}"/>
      <parent link="${tf_prefix}flange"/>
      <child link="${tf_prefix}tool0"/>
    </joint>

  </xacro:macro>
</robot>
