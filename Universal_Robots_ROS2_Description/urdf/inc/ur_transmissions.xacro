<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro">
  <!--
    NOTE the macro defined in this file is NOT part of the public API of this
          package. Users CANNOT rely on this file being available, or stored in
          this location. Nor can they rely on the existence of the macro.
  -->

  <xacro:macro name="ur_arm_transmission" params="prefix hw_interface">

    <transmission name="${prefix}shoulder_pan_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}shoulder_pan_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}shoulder_pan_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

    <transmission name="${prefix}shoulder_lift_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}shoulder_lift_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}shoulder_lift_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

    <transmission name="${prefix}elbow_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}elbow_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}elbow_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

    <transmission name="${prefix}wrist_1_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}wrist_1_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}wrist_1_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

    <transmission name="${prefix}wrist_2_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}wrist_2_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}wrist_2_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

    <transmission name="${prefix}wrist_3_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${prefix}wrist_3_joint">
        <hardwareInterface>${hw_interface}</hardwareInterface>
      </joint>
      <actuator name="${prefix}wrist_3_motor">
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>

  </xacro:macro>

</robot>
