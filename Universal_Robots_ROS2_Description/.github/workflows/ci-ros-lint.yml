name: <PERSON><PERSON>2 Lint
on:
  pull_request:

jobs:
  ament_lint:
    name: ament_${{ matrix.linter }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
          linter: [copyright, lint_cmake]
    steps:
    - uses: actions/checkout@v4
    - uses: ros-tooling/setup-ros@v0.7
    - uses: ros-tooling/action-ros-lint@v0.1
      with:
        distribution: rolling
        linter: ${{ matrix.linter }}
        package-name:
          ur_description
