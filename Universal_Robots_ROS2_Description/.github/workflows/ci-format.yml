# This is a format job. Pre-commit has a first-party GitHub action, so we use
# that: https://github.com/pre-commit/action

name: Format

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - ros2

jobs:
  pre-commit:
    name: Format
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - uses: pre-commit/action@v3.0.0
        with:
          extra_args: --all-files --hook-stage manual
