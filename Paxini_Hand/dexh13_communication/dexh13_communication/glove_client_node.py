#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
import serial
import threading
from my_interfaces.srv import SetJointAngles
import time

# 每个数据对应的原始数据范围
GLOVE_RANGES = [
    (128, 180),
    (128, 195),
    (128, 181),
    (129, 166),
    (125, 197),
    (129, 185),
    (130, 178),
    (121, 198),
    (129, 175),
    (128, 185),
    (122, 198),
    (129, 165),
    (129, 160),
    (127, 193),
    (131, 166)
]

# 静止阈值：整帧最大差值 ≤ 3 视为静止
STILL_THRESHOLD = 3

class GloveClientNode(Node):
    def __init__(self):
        super().__init__('glove_client_node')

        # ------------- 串口初始化 -------------
        self.serial = serial.Serial('/dev/ttyUSB0', 9600, timeout=0.1)
        if not self.serial.is_open:
            self.get_logger().error("无法打开串口 /dev/ttyUSB0")
            exit(-1)
        self.get_logger().info("串口已连接")

        # ------------- 客户端 -------------
        self.cli = self.create_client(SetJointAngles, 'set_joint_angles')
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info("等待 /set_joint_angles 服务...")
        self.get_logger().info("服务已就绪")

        self.running = True
        self.read_thread = threading.Thread(target=self.read_loop, daemon=True)
        self.read_thread.start()
        # --- 防抖缓存 ---
        # self.last_frame = [0] * 15   # 上一次的 15 字节
        self.last_frame_sent = [0] * 15   # 上一次真正发出去的 15 字节

        # 新增标志位
        self.active = False
        self.calibration_mode = False
        self.processing_mode = False

    # ------------------------------------------------------------------
    def read_loop(self):
        buffer = b''
        while self.running and rclpy.ok():
            if self.serial.in_waiting > 0:
                buffer += self.serial.read(self.serial.in_waiting)
                frames = self.process_raw_data(buffer)
                buffer = buffer[sum(len(frame) for frame in frames):]  # 移除已处理的数据
                for frame in frames:
                    if len(frame) == 15:
                        # 逐字节防抖（±2）
                        filtered = []
                        for new, old in zip(frame, self.last_frame_sent):
                            if abs(new - old) <= 2:
                                filtered.append(old)
                            else:
                                filtered.append(new)

                        # 静止阈值：整帧最大差值 ≤ STILL_THRESHOLD → 视为静止
                        max_diff = max(abs(a - b) for a, b in zip(filtered, self.last_frame_sent))
                        if max_diff <= STILL_THRESHOLD:
                            # 静止，不发指令
                            continue

                        # 真正变化，更新缓存并发送
                        self.last_frame_sent = filtered.copy()

                        self.get_logger().info(f"原始手套数据: {frame}")
                        self.get_logger().info(f"过滤后数据 : {filtered}")
                        angles = self.map_glove_to_angles(filtered)
                        self.get_logger().info(f"映射角度(deg): {angles}")
                        self.send_angles(angles)
            time.sleep(0.01)

    # ------------------------------------------------------------------
    def process_raw_data(self, raw_data):
        """处理原始串口数据，提取有效帧"""
        # 将字节数据转换为十六进制字符串
        hex_data = ''.join(f"{byte:02X}" for byte in raw_data)
        self.get_logger().debug(f"Received hex data: {hex_data}")
        
        # 帧解析逻辑
        frame_head = "01"  # 帧头标记
        frame_tail = "02"  # 帧尾标记
        
        # 查找所有帧头和帧尾的位置
        head_positions = [i for i in range(len(hex_data)) if hex_data.startswith(frame_head, i)]
        tail_positions = [i for i in range(len(hex_data)) if hex_data.startswith(frame_tail, i)]
        
        valid_frames = []
        # 提取有效的帧
        for head_pos in head_positions:
            # 找到对应的帧尾
            for tail_pos in tail_positions:
                if tail_pos > head_pos + len(frame_head):
                    # 提取帧数据（不包括帧头和帧尾）
                    frame_data_hex = hex_data[head_pos + len(frame_head):tail_pos]
                    
                    # 确保帧数据长度是偶数（每个字节由两个十六进制字符表示）
                    if len(frame_data_hex) % 2 == 0:
                        frame_data_hex_list = [frame_data_hex[i:i+2] for i in range(0, len(frame_data_hex), 2)]
                        frame_data_dec = [int(byte, 16) for byte in frame_data_hex_list]
                        
                        # 只要接收到有效数据就激活控制器
                        if not self.active:
                            self.active = True
                            self.get_logger().info("Controller activated")
                        
                        # 仅在激活状态处理数据
                        if self.active:
                            if self.calibration_mode:
                                self.calibrate(frame_data_dec)
                            elif self.processing_mode:
                                self.process_calibration_data()
                            else:
                                if len(frame_data_dec) == 15:
                                    valid_frames.append(frame_data_dec)
                    
                    # 处理完一个帧后，继续查找下一个帧
                    break
        return valid_frames

    # ------------------------------------------------------------------
    def map_glove_to_angles(self, frame):
        """15 字节 -> 16 角度（角度制）"""
        # 顺序映射（缺失位用同组前一个值补齐）
        indices = [3, 4, 5, 5,   # 食指
                6, 7, 8, 8,   # 中指
                9, 10, 11, 11, # 环指
                0, 1, 2, 2]  # 大拇指
        raw_vals = [frame[i] for i in indices]

        # 目标范围
        ranges = [
            (-20, 20), (0, 90), (0, 90), (0, 90),  # 大拇指
            (-20, 20), (0, 90), (0, 90), (0, 90),  # 食指
            (-20, 20), (0, 90), (0, 90), (0, 90),  # 中指
            (-20, 20), (0, 90), (0, 90), (0, 90)   # 环指
        ]

        def map_value(x, in_min, in_max, out_min, out_max):
            x = max(in_min, min(x, in_max))
            return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

        # 特殊处理大拇指的最后一个索引 2
        modified_raw_vals = raw_vals.copy()
        modified_raw_vals[-1] = modified_raw_vals[-1] / 2

        return [map_value(v, GLOVE_RANGES[indices[i]][0], GLOVE_RANGES[indices[i]][1], r[0], r[1]) for i, (v, r) in enumerate(zip(modified_raw_vals, ranges))]

    # def map_glove_to_angles(self, frame):
    #     """15 字节 -> 16 角度（角度制）"""
    #     # 顺序映射（缺失位用同组前一个值补齐）
    #     indices = [3, 4, 5, 5,   # 食指
    #                6, 7, 8, 8,   # 中指
    #                9, 10, 11, 11, # 环指
    #                0, 1, 2, 2]  # 大拇指
    #     raw_vals = [frame[i] for i in indices]

    #     # 目标范围
    #     ranges = [
    #         (-20, 20), (0, 90), (0, 90), (0, 90),  # 大拇指
    #         (-20, 20), (0, 90), (0, 90), (0, 90),  # 食指
    #         (-20, 20), (0, 90), (0, 90), (0, 90),  # 中指
    #         (-20, 20), (0, 90), (0, 90), (0, 90)   # 环指
    #     ]

    #     def map_value(x, in_min, in_max, out_min, out_max):
    #         x = max(in_min, min(x, in_max))
    #         return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

    #     # 修正索引问题
    #     return [map_value(v, GLOVE_RANGES[indices[i]][0], GLOVE_RANGES[indices[i]][1], r[0], r[1]) for i, (v, r) in enumerate(zip(raw_vals, ranges))]

    # ------------------------------------------------------------------
    def send_angles(self, angles):
        req = SetJointAngles.Request()
        req.joint_angles = angles
        future = self.cli.call_async(req)
        future.add_done_callback(self.handle_response)

    def handle_response(self, future):
        try:
            resp = future.result()
            if resp.success:
                self.get_logger().info("手套→灵巧手同步成功")
            else:
                self.get_logger().error(f"同步失败：{resp.message}")
        except Exception as e:
            self.get_logger().error(str(e))

    def destroy_node(self):
        self.running = False
        if self.serial.is_open:
            self.serial.close()
        super().destroy_node()

    def calibrate(self, frame_data_dec):
        # 校准逻辑
        pass

    def process_calibration_data(self):
        # 处理校准数据逻辑
        pass

    def process_data(self, frame_data_dec):
        # 处理数据逻辑
        pass

# ------------------------------------------------------------------
def main(args=None):
    rclpy.init(args=args)
    node = GloveClientNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()



# #!/usr/bin/env python3
# import rclpy
# from rclpy.node import Node
# import serial
# import threading
# from my_interfaces.srv import SetJointAngles
# import time

# # 原始数据范围
# GLOVE_MIN = 120.0
# GLOVE_MAX = 200.0
# # 静止阈值：整帧最大差值 ≤ 3 视为静止
# STILL_THRESHOLD = 3

# class GloveClientNode(Node):
#     def __init__(self):
#         super().__init__('glove_client_node')

#         # ------------- 串口初始化 -------------
#         self.serial = serial.Serial('/dev/ttyUSB0', 9600, timeout=0.1)
#         if not self.serial.is_open:
#             self.get_logger().error("无法打开串口 /dev/ttyUSB0")
#             exit(-1)
#         self.get_logger().info("串口已连接")

#         # ------------- 客户端 -------------
#         self.cli = self.create_client(SetJointAngles, 'set_joint_angles')
#         while not self.cli.wait_for_service(timeout_sec=1.0):
#             self.get_logger().info("等待 /set_joint_angles 服务...")
#         self.get_logger().info("服务已就绪")

#         self.running = True
#         self.read_thread = threading.Thread(target=self.read_loop, daemon=True)
#         self.read_thread.start()
#         # --- 防抖缓存 ---
#         # self.last_frame = [0] * 15   # 上一次的 15 字节
#         self.last_frame_sent = [0] * 15   # 上一次真正发出去的 15 字节

#         # 新增标志位
#         self.active = False
#         self.calibration_mode = False
#         self.processing_mode = False

#     # ------------------------------------------------------------------
#     def read_loop(self):
#         buffer = b''
#         while self.running and rclpy.ok():
#             if self.serial.in_waiting > 0:
#                 buffer += self.serial.read(self.serial.in_waiting)
#                 frames = self.process_raw_data(buffer)
#                 buffer = buffer[sum(len(frame) for frame in frames):]  # 移除已处理的数据
#                 for frame in frames:
#                     if len(frame) == 15:
#                         # 逐字节防抖（±2）
#                         filtered = []
#                         for new, old in zip(frame, self.last_frame_sent):
#                             if abs(new - old) <= 2:
#                                 filtered.append(old)
#                             else:
#                                 filtered.append(new)

#                         # 静止阈值：整帧最大差值 ≤ STILL_THRESHOLD → 视为静止
#                         max_diff = max(abs(a - b) for a, b in zip(filtered, self.last_frame_sent))
#                         if max_diff <= STILL_THRESHOLD:
#                             # 静止，不发指令
#                             continue

#                         # 真正变化，更新缓存并发送
#                         self.last_frame_sent = filtered.copy()

#                         self.get_logger().info(f"原始手套数据: {frame}")
#                         self.get_logger().info(f"过滤后数据 : {filtered}")
#                         angles = self.map_glove_to_angles(filtered)
#                         self.get_logger().info(f"映射角度(deg): {angles}")
#                         self.send_angles(angles)
#             time.sleep(0.01)

#     # ------------------------------------------------------------------
#     def process_raw_data(self, raw_data):
#         """处理原始串口数据，提取有效帧"""
#         # 将字节数据转换为十六进制字符串
#         hex_data = ''.join(f"{byte:02X}" for byte in raw_data)
#         self.get_logger().debug(f"Received hex data: {hex_data}")
        
#         # 帧解析逻辑
#         frame_head = "01"  # 帧头标记
#         frame_tail = "02"  # 帧尾标记
        
#         # 查找所有帧头和帧尾的位置
#         head_positions = [i for i in range(len(hex_data)) if hex_data.startswith(frame_head, i)]
#         tail_positions = [i for i in range(len(hex_data)) if hex_data.startswith(frame_tail, i)]
        
#         valid_frames = []
#         # 提取有效的帧
#         for head_pos in head_positions:
#             # 找到对应的帧尾
#             for tail_pos in tail_positions:
#                 if tail_pos > head_pos + len(frame_head):
#                     # 提取帧数据（不包括帧头和帧尾）
#                     frame_data_hex = hex_data[head_pos + len(frame_head):tail_pos]
                    
#                     # 确保帧数据长度是偶数（每个字节由两个十六进制字符表示）
#                     if len(frame_data_hex) % 2 == 0:
#                         frame_data_hex_list = [frame_data_hex[i:i+2] for i in range(0, len(frame_data_hex), 2)]
#                         frame_data_dec = [int(byte, 16) for byte in frame_data_hex_list]
                        
#                         # 只要接收到有效数据就激活控制器
#                         if not self.active:
#                             self.active = True
#                             self.get_logger().info("Controller activated")
                        
#                         # 仅在激活状态处理数据
#                         if self.active:
#                             if self.calibration_mode:
#                                 self.calibrate(frame_data_dec)
#                             elif self.processing_mode:
#                                 self.process_calibration_data()
#                             else:
#                                 if len(frame_data_dec) == 15:
#                                     valid_frames.append(frame_data_dec)
                    
#                     # 处理完一个帧后，继续查找下一个帧
#                     break
#         return valid_frames

#     # ------------------------------------------------------------------
#     def map_glove_to_angles(self, frame):
#         """15 字节 -> 16 角度（角度制）"""
#         # 顺序映射（缺失位用同组前一个值补齐）
#         indices = [3, 4, 5, 5,   # 食指
#                    6, 7, 8, 8,   # 中指
#                    9, 10, 11, 11, # 环指
#                    0, 1, 2, 2,]  # 大拇指
#         raw_vals = [frame[i] for i in indices]

#         # 目标范围
#         ranges = [
#             (-20, 20), (0, 90), (0, 90), (0, 90),  # 大拇指
#             (-20, 20), (0, 90), (0, 90), (0, 90),  # 食指
#             (-20, 20), (0, 90), (0, 90), (0, 90),  # 中指
#             (-20, 20), (0, 90), (0, 90), (0, 90)   # 环指
#         ]

#         def map_value(x, in_min, in_max, out_min, out_max):
#             x = max(in_min, min(x, in_max))
#             return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

#         return [map_value(v, GLOVE_MIN, GLOVE_MAX, r[0], r[1]) for v, r in zip(raw_vals, ranges)]

#     # ------------------------------------------------------------------
#     def send_angles(self, angles):
#         req = SetJointAngles.Request()
#         req.joint_angles = angles
#         future = self.cli.call_async(req)
#         future.add_done_callback(self.handle_response)

#     def handle_response(self, future):
#         try:
#             resp = future.result()
#             if resp.success:
#                 self.get_logger().info("手套→灵巧手同步成功")
#             else:
#                 self.get_logger().error(f"同步失败：{resp.message}")
#         except Exception as e:
#             self.get_logger().error(str(e))

#     def destroy_node(self):
#         self.running = False
#         if self.serial.is_open:
#             self.serial.close()
#         super().destroy_node()

#     def calibrate(self, frame_data_dec):
#         # 校准逻辑
#         pass

#     def process_calibration_data(self):
#         # 处理校准数据逻辑
#         pass

#     def process_data(self, frame_data_dec):
#         # 处理数据逻辑
#         pass

# # ------------------------------------------------------------------
# def main(args=None):
#     rclpy.init(args=args)
#     node = GloveClientNode()
#     try:
#         rclpy.spin(node)
#     except KeyboardInterrupt:
#         pass
#     finally:
#         node.destroy_node()
#         rclpy.shutdown()


# if __name__ == '__main__':
#     main()


