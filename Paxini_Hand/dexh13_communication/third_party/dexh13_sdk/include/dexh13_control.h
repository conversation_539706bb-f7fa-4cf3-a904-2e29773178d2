/**
 ******************************************************************************
 * @file: dexh13_control.h
 * @brief: This file contains the declaration of the DexH13Control class.
 * @version: 1.0.0

 ******************************************************************************
 */
#ifndef DEXH13_CONTROL_SDK_H
#define DEXH13_CONTROL_SDK_H

#include <iostream>
#include <memory>
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

#include "dexh13_define.h"
namespace paxini::bot {
class DexH13Control {
 public:
  DexH13Control();
  virtual ~DexH13Control();

  /**
   * @description: 灵巧手连接功能（单手模式）
   * @param handy_port_num: 灵巧手串口编号
   * @param camera_port_num: 相机串口编号
   * @return Connect return LEFT_HAND=1 or RIGHT_HAND=2
   * */

  // 激活灵巧手，成功返回左右手的连接状态，失败中止程序
  int activeHandy(const std::string& handy_port_num, const std::string& camera_port_num = "none");

  // 断开灵巧手连接
  int disconnectHandy();

  // 检查灵巧手连接状态，返回是否连接成功，成功返回true，失败返回false
  bool isConnectHandy();

  /**
   * @description: 设置配置信息
   * @param mode : 手指电机控制模式
   * @param max_current : 电机最大电流
   * @param max_speed : 电机最大速度
   * @return set return true or false
   * */
  // 自定义手指电机控制模式
  bool setMotorControlMode(const ControlMode mode);

  // 自定义电机最大电流
  bool setMotorMaxCurrent(uint16_t max_current);

  // 自定义电机最大速度
  bool setMotorMaxSpeed(uint16_t max_speed);

  /**
   * @description: 使能
   * @return motorable true or false
   * */
  // 手指电机使能
  bool enableMotor();

  // 手指电机下使能
  bool disableMotor();

  // 查询手指电机使能状态
  bool isMotorEnabled();

  /**
   * @discription: 控制
   * @param joint_angles：角度对应4个手指，indexFinger: 食指角度 middleFinger: 中指角度 ringFinger: 环指角度 thumb:
   * 大拇指角度
   * @param serial_rad 弧度对应手指的 13 个关节弧度
   */
  // 控制手指关节角度
  int setJointPositionsAngle(const std::vector<FingerAngle>& joint_angles);

  // 驱动手指关节弧度空间
  int setJointPositionsRadian(const std::vector<double>& serial_rad);

  // 设置电机目标电流
  int setMotorTargetCurrent(const Dex13MotorCurrent& target_current);

  // 设置电机目标速度
  int setMotorTargetSpeed(const Dex13MotorSpeed& target_speed);

  // 灵巧手触觉传感器（校准操作）
  bool calibrateSensor();

  /**
   * @description: 查询
   *
   * */
  // 获取手指关节角度
  void getJointPositionsAngle(std::vector<FingerAngle>& joint_angles);

  // 获取关节弧度
  void getJointPositionsRadian(std::vector<double>& serial_rad);

  // 查询控制模式
  ControlMode getMotorControlMode();

  // 查询手指触觉传感器信息
  std::vector<DexFingerTac> getFingerTactile();

  // 查询手指触觉传感器详细信息
  std::vector<DexFingerTac> getFingerTactileDetail();

  // 查询传感器温度数据
  std::vector<FingerTemp> getTemperature();

  // 查询全部数据（包括合力、阵列力和温度）
  void getAllTactileData(std::vector<DexFingerTac>& finger_tac, std::vector<DexFingerTac>& finger_tac_detail,
                         std::vector<FingerTemp>& temperature);

  /**
   * @description: 故障
   *
   * */
  // 手指故障状态 - 查询是否发生故障
  bool isFault();

  // 整机故障状态 - 查询当前故障原因
  std::vector<FingerMotorsError> getFaultCode();

  // 清除故障码
  bool clearFaultCode();

  /**
   * @description: 图像功能
   *
   * */

  // 获取当前帧 @return 相机一帧图像，如果相机未打开则返回空Mat 默认像素尺寸是640x720
  cv::Mat getFrame();

  // 显示一帧图片
  void showImage(const std::string& ImgName, const cv::Mat& Img);

  // 保存一帧图片
  void saveImage(const std::string& imgPath, const cv::Mat& img);

  // 相机参数包括：分辨率和帧率
  bool setCameraConfig(int width, int height, float fps);

  // 图像校正（去畸变）@return 去畸变的图像
  cv::Mat undistortImage(const cv::Mat& distortedFrame, const cv::Mat& cameraMatrix, const cv::Mat& distCoeffs);

  // 内参矩阵和畸变系数 Get the intrinsic matrix and distortion coefficients for the camera
  // @param width 相机的宽度 width the width of the camera resolution
  // @param height 相机的高度 height the height of the camera resolution
  // @return 包含内参矩阵和畸变系数的pair a pair containing the intrinsic matrix and distortion coefficients
  std::pair<cv::Mat, cv::Mat> getIntrinsicMatrixAndDistCoeffs(int width, int height);

  // 外参数矩阵 Get the extrinsic matrix for the camera
  cv::Mat getHandEyeMatrix();

  /**
   * @description: 版本信息
   */
  // 灵巧手固件版本
  const std::string getFirmwareVersion();

  // SDK版本
  const std::string getSDKVersion();

 private:
  std::unique_ptr<DexH13ControlImpl> impl_;
};

}  // namespace paxini::bot
#endif  // DEXH13_CONTROL_SDK_H
