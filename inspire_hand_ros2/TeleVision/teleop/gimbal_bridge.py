#!/usr/bin/env python3.10
"""
ROS2 Gimbal Bridge - 独立的ROS2客户端
用于接收来自teleop程序的角度数据并发送给云台服务
注意：需要在inspire_hand_ros2工作空间中运行
"""

import rclpy
from rclpy.node import Node
from example_interfaces.srv import AddTwoInts
import socket
import json
import threading
import time

class GimbalBridge(Node):
    def __init__(self):
        super().__init__('gimbal_bridge')
        
        # 创建ROS2客户端
        self.client = self.create_client(AddTwoInts, 'set_gimbal_angles')
        
        # 等待服务
        self.get_logger().info('等待云台服务...')
        if self.client.wait_for_service(timeout_sec=10.0):
            self.get_logger().info('✓ 云台服务已连接')
        else:
            self.get_logger().error('✗ 云台服务连接超时')
            return
        
        # 创建UDP服务器接收角度数据
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.sock.bind(('localhost', 9999))
        self.sock.settimeout(1.0)
        
        self.get_logger().info('✓ UDP服务器启动在端口9999')
        self.get_logger().info('等待来自teleop程序的角度数据...')
        
        # 启动接收线程
        self.running = True
        self.receive_thread = threading.Thread(target=self.receive_angles)
        self.receive_thread.start()
    
    def receive_angles(self):
        """接收角度数据的线程"""
        while self.running:
            try:
                data, addr = self.sock.recvfrom(1024)
                angle_data = json.loads(data.decode())
                
                yaw = angle_data['yaw']
                pitch = angle_data['pitch']
                
                # 发送到云台服务
                self.send_to_gimbal(yaw, pitch)
                
            except socket.timeout:
                continue
            except Exception as e:
                self.get_logger().error(f'接收数据错误: {e}')
    
    def send_to_gimbal(self, yaw_deg, pitch_deg):
        """发送角度到云台服务"""
        try:
            # 限制角度范围
            yaw_deg = max(-120, min(120, yaw_deg))
            pitch_deg = max(-120, min(120, pitch_deg))
            
            # 创建请求
            req = AddTwoInts.Request()
            req.a = int(yaw_deg * 100)
            req.b = int(pitch_deg * 100)
            
            # 异步发送
            future = self.client.call_async(req)
            
            # 简单的成功日志
            self.get_logger().debug(f'发送角度: yaw={yaw_deg:.1f}°, pitch={pitch_deg:.1f}°')
            
        except Exception as e:
            self.get_logger().error(f'发送角度错误: {e}')
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        if hasattr(self, 'receive_thread'):
            self.receive_thread.join()
        self.sock.close()

def main():
    rclpy.init()
    bridge = GimbalBridge()
    
    try:
        rclpy.spin(bridge)
    except KeyboardInterrupt:
        bridge.get_logger().info('收到退出信号')
    finally:
        bridge.cleanup()
        bridge.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
