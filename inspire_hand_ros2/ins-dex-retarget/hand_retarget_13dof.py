"""
Author: <PERSON><PERSON>
Date: 2025-07-23
Version: 2.0.0
Description: Hand retargeting for 13 DOF dexterous hand
             Thumb(4) + Ring(3) + Middle(3) + Index(3) = 13 DOF
"""

import numpy as np


def calculate_angle_between_vectors(v1, v2):
    # v1, v2为numpy数组，shape为(3,)
    
    # Check for zero vectors to prevent division by zero
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    
    if norm_v1 == 0 or norm_v2 == 0:
        return np.pi / 2
    
    cos_theta = np.dot(v1, v2) / (norm_v1 * norm_v2)
    cos_theta = np.clip(cos_theta, -1.0, 1.0)
    
    # Additional check for NaN values
    if np.isnan(cos_theta):
        return np.pi / 2
    
    angle_radians = np.arccos(cos_theta)
    return angle_radians


class HandRetarget13DOF:
    def __init__(self):
        # 13 DOF hand parameters
        # Thumb (4 DOF): CMC abduction, CMC flexion, MCP flexion, IP flexion
        self.thumb_cmc_abduction_limits = (-20.0, 20.0)    # CMC外展角度限制
        self.thumb_cmc_flexion_limits = (0.0, 90.0)      # CMC屈曲角度限制
        self.thumb_mcp_limits = (0.0, 90.0)              # MCP屈曲角度限制
        self.thumb_ip_limits = (0.0, 90.0)               # IP屈曲角度限制
        
        # Three fingers (Ring, Middle, Index) - each has 3 DOF: MCP1, MCP2, PIP
        self.finger_mcp1_limits = (-20.0, 20.0)          # MCP1侧摆
        self.finger_mcp2_limits = (0.0, 90.0)            # MCP2主屈曲
        self.finger_pip_limits = (0.0, 90.0)             # PIP屈曲

        # OK手势参数（简化版）
        self.pinch_threshold = 0.025  # 拇指和食指距离阈值（2.5cm）用于捏取动作

        # cache
        self.last_valid_left = None
        self.last_valid_right = None

    def _get_point_angle(self, finger_frames, origin, point1, point2):
        """计算三点之间的角度 - 返回关节屈曲角度（0度=伸直，90度=完全弯曲）"""
        vector1 = finger_frames[point1, :3, 3] - finger_frames[origin, :3, 3]
        vector2 = finger_frames[point2, :3, 3] - finger_frames[origin, :3, 3]
        angle = calculate_angle_between_vectors(vector1, vector2) / np.pi * 180
        
        # 将角度转换为关节屈曲角度：
        # 当手指伸直时，两个向量夹角接近180度，屈曲角度应该是0度
        # 当手指弯曲时，两个向量夹角变小，屈曲角度增大
        flexion_angle = 180.0 - angle
        
        # 确保角度在合理范围内
        flexion_angle = max(0.0, min(90.0, flexion_angle))
        
        return flexion_angle

    def _detect_pinch_gesture(self, finger_frames):
        """检测拇指和食指是否在做捏取动作
        
        简化的检测：只检查拇指尖和食指尖的距离
        """
        
        try:
            # 获取拇指尖和食指尖位置
            thumb_tip = finger_frames[4, :3, 3]      # handThumbTip
            index_tip = finger_frames[9, :3, 3]      # handIndexFingerTip
            
            # 计算距离
            thumb_index_distance = np.linalg.norm(thumb_tip - index_tip)
            
            # 判断是否在捏取
            is_pinching = thumb_index_distance < self.pinch_threshold
            
            return is_pinching, thumb_index_distance
            
        except (IndexError, TypeError):
            return False, float('inf')
    
    def _adjust_for_pinch(self, thumb_angles, three_finger_angles, pinch_strength):
        """为捏取动作调整拇指和食指角度
        
        Args:
            thumb_angles: 拇指4个角度 [cmc_abduction, cmc_flexion, mcp_flexion, ip_flexion]
            three_finger_angles: 三个手指9个角度 [ring*3, middle*3, index*3]
            pinch_strength: 捏取强度 (0.0-1.0)
        """
        
        # 调整拇指角度：使其向食指方向弯曲 - 进一步增大拇指调整幅度
        # thumb_angles[0] = thumb_angles[0] * (1.0 - pinch_strength * 1.2)  # 减少外展，向内收拢 (0.9->1.2)
        thumb_angles[0] = 0.0  # 捏取时直接设置CMC外展为0°，实现最佳对齐
        thumb_angles[1] = max(thumb_angles[1], 35.0 + 60.0 * pinch_strength)  # CMC屈曲 (30+50->35+60)
        thumb_angles[2] = max(thumb_angles[2], 45.0 + 55.0 * pinch_strength)  # MCP屈曲 (40+50->45+55)
        thumb_angles[3] = max(thumb_angles[3], 25.0 + 60.0 * pinch_strength)  # IP屈曲 (20+50->25+60)
        
        # 调整食指角度：使其向拇指方向弯曲形成捏取姿态 - 增大调整幅度
        # index finger在three_finger_angles的最后3个位置 [6:9]
        three_finger_angles[6] = three_finger_angles[6] - 12.0 * pinch_strength  # 向内侧摆，靠近拇指 (8->12)
        three_finger_angles[7] = max(three_finger_angles[7], 25.0 + 55.0 * pinch_strength)  # MCP屈曲 (20+40->25+55)
        three_finger_angles[8] = max(three_finger_angles[8], 30.0 + 60.0 * pinch_strength)  # PIP屈曲 (25+45->30+60)
        
        return thumb_angles, three_finger_angles

    def _get_mcp_abduction_angle(self, finger_frames, finger_idx):
        """计算MCP关节的侧摆角度 - 基于手指相对于手掌中心的偏移
        
        根据正确的索引：
        - 食指: 5-9 (handIndexFingerMetacarpal - handIndexFingerTip)
        - 中指: 10-14 (handMiddleFingerMetacarpal - handMiddleFingerTip) 
        - 无名指: 15-19 (handRingFingerMetacarpal - handRingFingerTip)
        """
        
        if finger_idx == 1:  # middle finger (中指)
            # 中指作为参考，侧摆角度为0
            return 0.0
        
        # 计算手掌的参考平面
        wrist = finger_frames[0, :3, 3]  # handWrist
        middle_base = finger_frames[10, :3, 3]  # handMiddleFingerMetacarpal
        
        if finger_idx == 0:  # ring finger (无名指)
            ring_base = finger_frames[15, :3, 3]    # handRingFingerMetacarpal
            
            # 计算无名指相对于中指的侧摆
            # 使用中指和无名指基部的距离投影到手掌平面
            middle_to_ring = ring_base - middle_base
            palm_forward = middle_base - wrist
            
            # 计算垂直于手掌前进方向的侧摆分量
            # 使用叉积计算手掌的侧向方向
            palm_up = np.array([0, 1, 0])  # 假设Y轴为向上方向
            palm_side = np.cross(palm_forward, palm_up)
            palm_side = palm_side / np.linalg.norm(palm_side)
            
            # 计算侧摆分量
            side_component = np.dot(middle_to_ring, palm_side)
            
            # 转换为角度（度）
            side_angle = np.arctan2(side_component, np.linalg.norm(palm_forward)) * 180 / np.pi
            
            # 映射到合理范围
            return np.clip(side_angle * 0.5, -20.0, 20.0)
            
        elif finger_idx == 2:  # index finger (食指)
            index_base = finger_frames[5, :3, 3]    # handIndexFingerMetacarpal
            
            # 计算食指相对于中指的侧摆
            middle_to_index = index_base - middle_base
            palm_forward = middle_base - wrist
            
            # 计算垂直于手掌前进方向的侧摆分量
            palm_up = np.array([0, 1, 0])  # 假设Y轴为向上方向
            palm_side = np.cross(palm_forward, palm_up)
            palm_side = palm_side / np.linalg.norm(palm_side)
            
            # 计算侧摆分量（食指在另一侧，所以取负值）
            side_component = -np.dot(middle_to_index, palm_side)
            
            # 转换为角度（度）
            side_angle = np.arctan2(side_component, np.linalg.norm(palm_forward)) * 180 / np.pi
            
            # 映射到合理范围
            return np.clip(side_angle * 0.5, -20.0, 20.0)
            
        return 0.0

    def _get_thumb_cmc_abduction_angle(self, finger_frames):
        """计算拇指CMC关节的外展角度
        
        根据正确的索引：
        拇指: 1-4 (handThumbKnuckle - handThumbTip)
        
        计算拇指相对于手掌平面的外展程度
        """
        
        try:
            # 获取关键点
            wrist = finger_frames[0, :3, 3]  # handWrist
            thumb_knuckle = finger_frames[1, :3, 3]  # handThumbKnuckle
            index_base = finger_frames[5, :3, 3]  # handIndexFingerMetacarpal
            middle_base = finger_frames[10, :3, 3]  # handMiddleFingerMetacarpal
            
            # 建立手掌坐标系
            # 手掌前向：从手腕到中指基部
            palm_forward = middle_base - wrist
            palm_forward = palm_forward / (np.linalg.norm(palm_forward) + 1e-8)
            
            # 手掌侧向：从中指基部到食指基部
            palm_side = index_base - middle_base
            palm_side = palm_side / (np.linalg.norm(palm_side) + 1e-8)
            
            # 手掌向上：通过叉积计算
            palm_up = np.cross(palm_forward, palm_side)
            palm_up = palm_up / (np.linalg.norm(palm_up) + 1e-8)
            
            # 计算拇指向量（从手腕到拇指关节）
            thumb_vector = thumb_knuckle - wrist
            
            # 将拇指向量投影到手掌平面上
            thumb_in_palm = thumb_vector - np.dot(thumb_vector, palm_up) * palm_up
            
            # 计算拇指在侧向（外展）方向上的分量
            side_component = np.dot(thumb_in_palm, palm_side)
            forward_component = np.dot(thumb_in_palm, palm_forward)
            
            # 计算外展角度
            abduction_angle = np.arctan2(side_component, forward_component) * 180.0 / np.pi
            
            # 限制在合理范围内，并调整缩放
            # 如果角度总是达到极限值，说明缩放过大
            return np.clip(abduction_angle * 0.3, -20.0, 20.0)
            
        except (IndexError, TypeError, ZeroDivisionError):
            return 0.0

    def _solve_three_fingers(self, finger_frames):
        """
        解算三根手指的角度：Ring, Middle, Index
        每根手指3个自由度：MCP1(侧摆), MCP2(屈曲), PIP(屈曲)
        返回9个角度值
        
        根据正确的索引：
        - 食指: 5-9 (handIndexFingerMetacarpal - handIndexFingerTip)
        - 中指: 10-14 (handMiddleFingerMetacarpal - handMiddleFingerTip) 
        - 无名指: 15-19 (handRingFingerMetacarpal - handRingFingerTip)
        """
        three_fingers_angles = np.zeros(9)  # 3 fingers × 3 DOF
        
        # 定义手指的索引
        fingers_info = [
            # (finger_idx, start_idx, name)
            (0, 15, "ring"),    # 无名指: 15-19
            (1, 10, "middle"),  # 中指: 10-14
            (2, 5, "index")     # 食指: 5-9
        ]
        
        for finger_idx, start_idx, finger_name in fingers_info:
            # 计算各关节索引
            metacarpal_idx = start_idx      # handXXXFingerMetacarpal
            knuckle_idx = start_idx + 1     # handXXXFingerKnuckle
            intermediate_base_idx = start_idx + 2  # handXXXFingerIntermediateBase
            intermediate_tip_idx = start_idx + 3   # handXXXFingerIntermediateTip
            tip_idx = start_idx + 4         # handXXXFingerTip
            
            # MCP1: 侧摆角度（掌指关节侧摆）
            mcp1_angle = self._get_mcp_abduction_angle(finger_frames, finger_idx)
            
            # MCP2: 主要屈曲角度（掌指关节屈伸）
            # 使用 knuckle 作为原点，metacarpal 和 tip 作为两个方向向量
            mcp2_angle = self._get_point_angle(finger_frames, knuckle_idx, metacarpal_idx, tip_idx)
            
            # PIP: 近端指间关节屈曲
            # 使用 intermediate_base 作为原点
            pip_angle = self._get_point_angle(finger_frames, intermediate_base_idx, knuckle_idx, intermediate_tip_idx)
            
            # 角度限制（直接输出角度值，不映射到0-1000）
            mcp1_angle = np.clip(mcp1_angle, *self.finger_mcp1_limits)
            mcp2_angle = np.clip(mcp2_angle, *self.finger_mcp2_limits)
            pip_angle = np.clip(pip_angle, *self.finger_pip_limits)
            
            # 存储角度（按照 ring, middle, index 的顺序）
            finger_start_idx = finger_idx * 3
            three_fingers_angles[finger_start_idx:finger_start_idx+3] = [mcp1_angle, mcp2_angle, pip_angle]
        
        return three_fingers_angles

    def _solve_thumb_4dof(self, finger_frames):
        """
        解算拇指的4个自由度：
        1. CMC abduction (外展/内收)
        2. CMC flexion (屈曲/伸展)  
        3. MCP flexion (掌指关节屈曲)
        4. IP flexion (指间关节屈曲)
        
        根据正确的索引：
        拇指: 1-4 (handThumbKnuckle - handThumbTip)
        1: handThumbKnuckle
        2: handThumbIntermediateBase
        3: handThumbIntermediateTip  
        4: handThumbTip
        """
        
        # CMC外展角度：使用拇指关节点计算
        cmc_abduction = self._get_thumb_cmc_abduction_angle(finger_frames)
        
        # CMC屈曲角度：改回使用手腕作为参考点，但调整基准偏移
        # 使用手腕、拇指关节、拇指尖三点计算，这样角度范围更稳定
        # cmc_flexion = self._get_point_angle(finger_frames, 1, 0, 4)  # 以1(thumbKnuckle)为原点，0(wrist)和4(thumbTip)为方向
        
        # CMC角度校正：增加基准偏移，解决角度过大的问题
        # cmc_flexion = max(0.0, cmc_flexion - 30.0)  # 增加基准偏移到30度
        
        cmc_flexion = self._get_point_angle(finger_frames, 6, 3, 21)          
        cmc_abduction = self._get_point_angle(finger_frames,1,2,6)
        mcp_flexion = self._get_point_angle(finger_frames,2,1,3)
        ip_flexion = self._get_point_angle(finger_frames,3,2,4)
        
        cmc_abduction = np.clip(cmc_abduction, *self.thumb_cmc_abduction_limits)/90 * 40 - 20
        cmc_flexion = np.clip(cmc_flexion, *self.thumb_cmc_flexion_limits)
        mcp_flexion = np.clip(mcp_flexion, *self.thumb_mcp_limits)
        ip_flexion = np.clip(ip_flexion, *self.thumb_ip_limits)
        
        cmc_flexion = min(cmc_flexion * 1.0, 90.0)  
        mcp_flexion = min(mcp_flexion * 1.0, 90.0)
        ip_flexion = min(ip_flexion * 1.0, 90.0)

        return np.array([cmc_abduction, cmc_flexion, mcp_flexion, ip_flexion])

    def solve_fingers_angles(self, r):
        """
        主函数：解算13自由度灵巧手的关节角度
        返回顺序：Thumb(4) + Ring(3) + Middle(3) + Index(3) = 13 DOF
        """
        
        # 初始化缓存
        if self.last_valid_left is None:
            self.last_valid_left = r["left_fingers"]
        if self.last_valid_right is None:
            self.last_valid_right = r["right_fingers"]

        # ============= 左手处理 =============
        finger_frames = r["left_fingers"]

        # 数据有效性检查
        if finger_frames[1][0, 0] == 0 and finger_frames[1][0, 1] == 0 and finger_frames[1][0, 2] == 0:
            # 使用缓存数据
            finger_frames = self.last_valid_left
        else:
            # 更新缓存
            self.last_valid_left = finger_frames

        # 解算左手角度
        left_thumb_angles = self._solve_thumb_4dof(finger_frames)  # 4 DOF
        left_three_fingers_angles = self._solve_three_fingers(finger_frames)       # 9 DOF
        
        # 移除捏取模式 - 通过拇指预弯曲自然实现抓取功能
        
        left_angles = np.concatenate((left_thumb_angles, left_three_fingers_angles))  # 13 DOF

        # ============= 右手处理 =============
        finger_frames = r["right_fingers"]

        # 数据有效性检查
        if finger_frames[1][0, 0] == 0 and finger_frames[1][0, 1] == 0 and finger_frames[1][0, 2] == 0:
            # 使用缓存数据
            finger_frames = self.last_valid_right
        else:
            # 更新缓存
            self.last_valid_right = finger_frames

        # 解算右手角度
        right_thumb_angles = self._solve_thumb_4dof(finger_frames)  # 4 DOF
        right_three_fingers_angles = self._solve_three_fingers(finger_frames)       # 9 DOF
        
        # 移除捏取模式 - 通过拇指预弯曲自然实现抓取功能
        
        right_angles = np.concatenate((right_thumb_angles, right_three_fingers_angles))  # 13 DOF

        return left_angles, right_angles

    def get_joint_names(self):
        """返回13个关节的名称，便于调试"""
        joint_names = [
            # Thumb (4 DOF)
            "thumb_cmc_abduction",
            "thumb_cmc_flexion", 
            "thumb_mcp_flexion",
            "thumb_ip_flexion",
            # Ring finger (3 DOF)
            "ring_mcp1_abduction",
            "ring_mcp2_flexion", 
            "ring_pip_flexion",
            # Middle finger (3 DOF)
            "middle_mcp1_abduction",
            "middle_mcp2_flexion",
            "middle_pip_flexion", 
            # Index finger (3 DOF)
            "index_mcp1_abduction",
            "index_mcp2_flexion",
            "index_pip_flexion"
        ]
        return joint_names

    def print_angles(self, left_angles, right_angles):
        """打印角度值，便于调试"""
        joint_names = self.get_joint_names()
        
        print("=== Left Hand Angles ===")
        for i, (name, angle) in enumerate(zip(joint_names, left_angles)):
            print(f"{i:2d}. {name:20s}: {angle:6.1f}")
        
        print("\n=== Right Hand Angles ===")
        for i, (name, angle) in enumerate(zip(joint_names, right_angles)):
            print(f"{i:2d}. {name:20s}: {angle:6.1f}")
        print()


# 使用示例
if __name__ == "__main__":
    # 创建13自由度手部重定向器
    hand_retarget = HandRetarget13DOF()
    
    # 打印关节名称
    print("13 DOF Hand Joint Names:")
    joint_names = hand_retarget.get_joint_names()
    for i, name in enumerate(joint_names):
        print(f"{i+1:2d}. {name}")
