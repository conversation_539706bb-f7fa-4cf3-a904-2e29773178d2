<?xml version="1.0"?>
<package format="3">
  <name>trac_ik_python</name>
  <version>1.6.6</version>
  <description>The trac_ik_python package contains a python wrapper using SWIG
  for trac_ik_lib</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <author><PERSON></author>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>rospy</build_depend>
  <build_depend>swig</build_depend>
  <build_depend>trac_ik_lib</build_depend>
  <build_depend>tf_conversions</build_depend>
  <build_depend>libnlopt-dev</build_depend>
  <build_depend condition="$ROS_DISTRO == noetic">libnlopt-cxx-dev</build_depend>

  <exec_depend>rospy</exec_depend>
  <exec_depend>swig</exec_depend>
  <exec_depend>trac_ik_lib</exec_depend>
  <exec_depend>tf_conversions</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>libnlopt-dev</exec_depend>
  <exec_depend>libnlopt0</exec_depend>

</package>
