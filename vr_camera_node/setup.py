from setuptools import find_packages, setup

package_name = 'vr_camera_node'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='terra',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            "vr_cam_node = vr_camera_node.television_paxini_bridge_ultimate:main",
            "vr_quest3_cam_node = vr_camera_node.quest3_cam_television:main",
        ],
    },
)
