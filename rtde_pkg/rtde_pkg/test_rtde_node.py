#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from rtde_control import RTDEControlInterface
from rtde_receive import RTDEReceiveInterface
import time
import statistics


class RTDENode(Node):

    def __init__(self,name):
        super().__init__(name)
        self.get_logger().info("大家好，我是%s!" % name)

        self.command_sub = self.create_subscription(Float64MultiArray,'/rtde_test/commands',self.command_callback,10)
        self.timer = self.create_timer(1.0/100,self.timer_callback)   
        # 连接 UR 机械臂
        self.rtde_c = RTDEControlInterface("************")  # 换成你的 UR IP
        self.rtde_r = RTDEReceiveInterface("************")  # 添加接收接口

        # 定义目标关节位置（弧度）
        q1 = [0.0, -1.57, 1.57, 0.0, 1.57, 0.0]
        q2 = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        
        acceleration = 0.5  # 归一化加速度
        speed = 0.5        # 归一化速度
        servo_time = 0.008  # 8ms (125Hz)
        lookahead_time = 0.1  # 100ms 前瞻
        gain = 500          # 伺服增益

        # 控制机械臂移动
        # rtde_c.moveJ(q1)  # Joint space movement
        # rtde_c.servoJ(q2,acceleration,speed,servo_time,lookahead_time,gain)

        # 保持位置 2 秒
        # time.sleep(2)

        # 关闭连接
        # rtde_c.stopScript()
        # rtde_c.servoStop()

        # # 连接 UR 机械臂（替换为你的 IP 地址）
        # rtde_r = RTDEReceiveInterface("************")

        # # 获取当前关节角度（单位：弧度）
        # joint_positions = rtde_r.getActualQ()

        # # 打印结果
        # print("当前关节角度（rad）:", joint_positions)

        init_cmd = [0.507,-1.26,1.65,-0.39,2.08,0.0]
        self.cmd = init_cmd

        self.acceleration=0.5
        self.speed=0.5
        self.time=0.008
        self.lookahead_time=0.1
        self.gain=300

        self.init_flag = False
        self.start_flag = True
        self.start_cnt = 0
        
        # 延迟测量相关变量
        self.servo_execution_times = []
        self.command_intervals = []
        self.robot_response_times = []
        self.last_command_time = None
        self.last_actual_q = None

    def timer_callback(self):
        if self.init_flag == False:
                self.rtde_c.moveJ(self.cmd)
                self.init_flag = True
        else:
            if self.start_flag == True:
                if self.start_cnt >= 600:
                    # 记录发送命令前的机械臂状态
                    actual_q_before = self.rtde_r.getActualQ()
                    
                    # 记录发送命令的时间
                    command_start_time = time.perf_counter()
                    
                    # 发送servoJ命令
                    self.rtde_c.servoJ(self.cmd,
                            self.acceleration,
                            self.speed,
                            self.time,
                            self.lookahead_time,
                            self.gain
                        )
                    
                    # 记录命令发送完成时间
                    command_end_time = time.perf_counter()
                    
                    # 测量servoJ调用本身的耗时
                    servo_call_time = (command_end_time - command_start_time) * 1000
                    self.servo_execution_times.append(servo_call_time)
                    
                    # 测量机械臂响应时间（位置变化）
                    actual_q_after = self.rtde_r.getActualQ()
                    if self.last_actual_q is not None:
                        position_change = sum(abs(a_after - a_before) for a_after, a_before in zip(actual_q_after, self.last_actual_q))
                        if position_change > 0.001:  # 如果有明显位置变化
                            # 计算从命令发送到位置变化的时间
                            response_end_time = time.perf_counter()
                            response_time = (response_end_time - command_start_time) * 1000
                            self.robot_response_times.append(response_time)
                    
                    self.last_actual_q = actual_q_after
                    
                    # 定期报告统计信息
                    if len(self.servo_execution_times) >= 50:
                        avg_servo = statistics.mean(self.servo_execution_times)
                        max_servo = max(self.servo_execution_times)
                        min_servo = min(self.servo_execution_times)
                        
                        self.get_logger().info(f"⏱️ ServoJ call timing - Avg: {avg_servo:.3f}ms, Max: {max_servo:.3f}ms, Min: {min_servo:.3f}ms")
                        self.servo_execution_times.clear()
                    
                    if len(self.robot_response_times) >= 30:
                        avg_response = statistics.mean(self.robot_response_times)
                        max_response = max(self.robot_response_times)
                        self.get_logger().info(f"🤖 Robot response timing - Avg: {avg_response:.3f}ms, Max: {max_response:.3f}ms")
                        self.robot_response_times.clear()
                    
                else:
                    self.rtde_c.moveJ(self.cmd)
                    self.start_cnt += 1
            else:
                current_pos = self.rtde_r.getActualQ()
                self.rtde_c.moveJ(current_pos)
        
    def command_callback(self, msg):
        # 记录命令接收时间
        current_time = time.perf_counter()
        
        if len(msg.data) == 6:
            self.cmd = msg.data
            
            # 测量命令间隔
            if self.last_command_time is not None:
                command_interval = (current_time - self.last_command_time) * 1000
                self.command_intervals.append(command_interval)
                
                # 定期报告命令频率
                if len(self.command_intervals) >= 30:
                    avg_interval = statistics.mean(self.command_intervals)
                    max_interval = max(self.command_intervals)
                    min_interval = min(self.command_intervals)
                    frequency = 1000 / avg_interval
                    
                    self.get_logger().info(f"📨 Command timing - Freq: {frequency:.2f}Hz, Avg: {avg_interval:.3f}ms, Max: {max_interval:.3f}ms, Min: {min_interval:.3f}ms")
                    self.command_intervals.clear()
            
            self.last_command_time = current_time
            self.get_logger().debug(f"新指令: {msg.data}", throttle_duration_sec=1)


def main(args=None):
    rclpy.init(args=args)
    node = RTDENode("rtde_node")
    rclpy.spin(node)
    rclpy.shutdown()
