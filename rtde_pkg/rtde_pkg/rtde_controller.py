#!/usr/bin/env python3
import rclpy
from sensor_msgs.msg import JointState
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from std_msgs.msg import Bool
from rclpy.qos import qos_profile_sensor_data
from rtde_control import RTDEControlInterface
from rtde_receive import RTDEReceiveInterface
import time

class LeftCtrlNode(Node):

    def __init__(self,name):
        super().__init__(name)
        self.get_logger().info("大家好，我是%s!" % name)
        
        self.command_sub = self.create_subscription(Float64MultiArray,'/ur_arm_ros2_controller/commands',self.command_callback,10)
        self.start_flag_sub = self.create_subscription(Bool,'/start_flag',self.start_flag_callback,qos_profile_sensor_data)
        self.timer = self.create_timer(1.0/100,self.timer_callback)     
        self.rtde_c = RTDEControlInterface("************")
        self.rtde_r = RTDEReceiveInterface("************")
        
        self.init_cmd = [1.57,-1.30,1.706,-0.36,1.64,-0.785]
        self.cmd = self.init_cmd
        
        self.init_flag = False
        self.start_flag = False
        self.start_cnt = 0
        
        self.acceleration=0.5
        self.speed=0.5
        self.time=0.008
        self.lookahead_time=0.1
        self.gain=300

        self.smooth_acceleration = 0.01
        self.smooth_speed = 0.02
        self.smooth_time = 0.008
        self.smooth_lookahead_time = 0.1
        self.smooth_gain = 150
        
        self.frequency = 0.0
        self.last_time = self.get_clock().now()

    def timer_callback(self):
        if self.init_flag == False:
                self.rtde_c.moveJ(self.cmd)
                self.init_flag = True
        else:
            if self.frequency > 30:
                if self.start_flag == True:
                    if self.start_cnt >= 300:
                        self.rtde_c.servoJ(self.cmd,
                                self.acceleration,
                                self.speed,
                                self.time,
                                self.lookahead_time,
                                self.gain
                            )
                    else:
                        self.rtde_c.servoJ(self.cmd,
                            self.smooth_acceleration,
                            self.smooth_speed,
                            self.smooth_time,
                            self.smooth_lookahead_time,
                            self.smooth_gain
                        )
                        self.start_cnt += 1
                else:
                    self.rtde_c.servoJ(self.init_cmd,
                            self.smooth_acceleration,
                            self.smooth_speed,
                            self.smooth_time,
                            self.smooth_lookahead_time,
                            self.smooth_gain
                        )
            else:
                current_pos = self.rtde_r.getActualQ()
                self.rtde_c.servoJ(current_pos,
                                self.smooth_acceleration,
                                self.smooth_speed,
                                self.smooth_time,
                                self.smooth_lookahead_time,
                                self.smooth_gain
                            )
        
    def command_callback(self, msg):
        current_time = self.get_clock().now()
        time_diff = (current_time - self.last_time).nanoseconds * 1e-9  # 转换为秒
        self.frequency = 1.0 / time_diff if time_diff > 0 else 0.0
        self.last_time = current_time
        if len(msg.data) == 6:
            self.cmd = msg.data
            self.get_logger().debug(f"新指令: {msg.data}", throttle_duration_sec=1)

    def start_flag_callback(self, msg):
        if msg is not None and hasattr(msg, 'data'):
            self.start_flag = msg.data
            
    def __del__(self):
        if hasattr(self, 'rtde'):
            self.rtde.servoStop()
            self.rtde.disconnect()


def main(args=None):
    rclpy.init(args=args)
    node = LeftCtrlNode("rtde_left_ctrl_node")
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()