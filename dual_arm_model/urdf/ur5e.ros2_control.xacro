<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:macro name="ur_arm_ros2_control" params="prefix command_topic">
    <xacro:property name="joint_cmd_topic" value="/${command_topic}" />
    <ros2_control name="${prefix}ur_arm_system" type="system">
      <hardware>
          <plugin>topic_based_ros2_control/TopicBasedSystem</plugin>
          <param name="joint_commands_topic">${joint_cmd_topic}</param>
          <param name="joint_states_topic">/joint_states</param>
      </hardware>
      <!-- Each joint definition -->
      <joint name="${prefix}shoulder_pan_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}shoulder_lift_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}elbow_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_1_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_2_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_3_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>
    </ros2_control>
  </xacro:macro>

</robot>
