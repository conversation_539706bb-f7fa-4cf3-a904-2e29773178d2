<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:macro name="inspire_hand_right" params="prefix parent_link *origin">
    <joint name="${prefix}fixed_hand_joint" type="fixed">
      <parent link="${parent_link}"/>
      <child link="${prefix}R_hand_base_link"/>
      <xacro:insert_block name="origin"/>
    </joint>

    <!-- Base link -->
    <link name="${prefix}R_hand_base_link">
      <inertial>
        <origin
          xyz="-0.0025264 -0.066047 0.0019598"
          rpy="0 0 0" />
        <mass
          value="0.14143" />
        <inertia
          ixx="0.00012281"
          ixy="2.1711E-06"
          ixz="1.7709E-06"
          iyy="8.3832E-05"
          iyz="-1.6551E-06"
          izz="7.6663E-05" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/R_hand_base_link.STL" />
        </geometry>
        <material name="${prefix}mat_grey">
          <color rgba="0.1 0.1 0.1 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/R_hand_base_link.STL" />
        </geometry>
      </collision>
    </link>

    <!-- Thumb -->
    <link name="${prefix}R_thumb_proximal_base">
      <inertial>
        <origin
          xyz="-0.0048064 0.0009382 -0.00757"
          rpy="0 0 0" />
        <mass
          value="0.0018869" />
        <inertia
          ixx="5.816E-08"
          ixy="1.4539E-08"
          ixz="4.491E-09"
          iyy="7.9161E-08"
          iyz="-1.8727E-09"
          izz="6.7433E-08" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_thumb_proximal_yaw_joint"
      type="revolute">
      <origin
        xyz="-0.01696 -0.0691 -0.02045"
        rpy="1.5708 -1.5708 0" />
      <parent
        link="${prefix}R_hand_base_link" />
      <child
        link="${prefix}R_thumb_proximal_base" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-0.1"
        upper="1.3"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_thumb_proximal">
      <inertial>
        <origin
          xyz="0.021932 0.012785 -0.0080386"
          rpy="0 0 0" />
        <mass
          value="0.0066075" />
        <inertia
          ixx="1.5686E-06"
          ixy="-7.8296E-07"
          ixz="8.9143E-10"
          iyy="1.7353E-06"
          iyz="-1.0191E-09"
          izz="2.786E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_thumb_proximal_pitch_joint"
      type="revolute">
      <origin
        xyz="-0.0088099 0.010892 -0.00925"
        rpy="1.5708 0 2.8587" />
      <parent
        link="${prefix}R_thumb_proximal_base" />
      <child
        link="${prefix}R_thumb_proximal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0.0"
        upper="0.5"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_thumb_intermediate">
      <inertial>
        <origin
          xyz="0.0095544 -0.0016282 -0.0071997"
          rpy="0 0 0" />
        <mass
          value="0.0037847" />
        <inertia
          ixx="3.6981E-07"
          ixy="-9.8581E-08"
          ixz="-4.7469E-12"
          iyy="3.2394E-07"
          iyz="1.0939E-12"
          izz="4.6531E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_thumb_intermediate_joint"
      type="revolute">
      <origin
        xyz="0.04407 0.034553 -0.0008"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_thumb_proximal" />
      <child
        link="${prefix}R_thumb_intermediate" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="0.8"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_thumb_proximal_pitch_joint"
        multiplier="1.6"
        offset="0" />
    </joint>

    <link name="${prefix}R_thumb_distal">
      <inertial>
        <origin
          xyz="0.0092888 0.0049529 -0.0060033"
          rpy="0 0 0" />
        <mass
          value="0.0033441" />
        <inertia
          ixx="1.3632E-07"
          ixy="-5.6788E-08"
          ixz="-9.2764E-11"
          iyy="1.4052E-07"
          iyz="-1.2283E-10"
          izz="2.0026E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_thumb_distal_joint"
      type="revolute">
      <origin
        xyz="0.020248 0.010156 -0.0012"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_thumb_intermediate" />
      <child
        link="${prefix}R_thumb_distal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.2"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_thumb_proximal_pitch_joint"
        multiplier="2.4"
        offset="0" />
    </joint>

    <link name="${prefix}R_thumb_tip">
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <sphere radius="0.005"/>
        </geometry>
        <material name="${prefix}mat_green">
          <color rgba="0 1 0 1" />
        </material>
      </visual>
    </link>

    <joint name="${prefix}R_thumb_tip_joint" type="fixed">
      <parent link="${prefix}R_thumb_distal"/>
      <child link="${prefix}R_thumb_tip"/>
      <origin rpy="0 0 0" xyz="0.015 0.013 -0.004"/>
    </joint>

    <!-- Index Finger -->
    <link name="${prefix}R_index_proximal">
      <inertial>
        <origin
          xyz="0.0012259 0.011942 -0.0060001"
          rpy="0 0 0" />
        <mass
          value="0.0042403" />
        <inertia
          ixx="6.6232E-07"
          ixy="-1.5775E-08"
          ixz="1.8515E-12"
          iyy="2.1146E-07"
          iyz="-5.0828E-12"
          izz="6.9398E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_index_proximal_joint"
      type="revolute">
      <origin
        xyz="0.00028533 -0.13653 -0.032268"
        rpy="-3.1067 0 0" />
      <parent
        link="${prefix}R_hand_base_link" />
      <child
        link="${prefix}R_index_proximal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_index_intermediate">
      <inertial>
        <origin
          xyz="0.0019697 0.019589 -0.005"
          rpy="0 0 0" />
        <mass
          value="0.0045683" />
        <inertia
          ixx="7.6111E-07"
          ixy="8.7637E-08"
          ixz="-3.7751E-13"
          iyy="9.6076E-08"
          iyz="9.9444E-13"
          izz="7.8179E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_index_intermediate_joint"
      type="revolute">
      <origin
        xyz="-0.0026138 0.032026 -0.001"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_index_proximal" />
      <child
        link="${prefix}R_index_intermediate" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_index_proximal_joint"
        multiplier="1"
        offset="0" />
    </joint>

    <link name="${prefix}R_index_tip">
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <sphere radius="0.005"/>
        </geometry>
        <material name="${prefix}mat_green">
          <color rgba="0 1 0 1" />
        </material>
      </visual>
    </link>

    <joint name="${prefix}R_index_tip_joint" type="fixed">
      <parent link="${prefix}R_index_intermediate"/>
      <child link="${prefix}R_index_tip"/>
      <origin rpy="0 0 0" xyz="-0.005 0.04 -0.004"/>
    </joint>

    <!-- Middle Finger -->
    <link name="${prefix}R_middle_proximal">
      <inertial>
        <origin
          xyz="0.001297 0.011934 -0.0060001"
          rpy="0 0 0" />
        <mass
          value="0.0042403" />
        <inertia
          ixx="6.6211E-07"
          ixy="-1.8461E-08"
          ixz="1.8002E-12"
          iyy="2.1167E-07"
          iyz="-6.6808E-12"
          izz="6.9397E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_middle_proximal_joint"
      type="revolute">
      <origin
        xyz="0.00028533 -0.1371 -0.01295"
        rpy="-3.1416 0 0" />
      <parent
        link="${prefix}R_hand_base_link" />
      <child
        link="${prefix}R_middle_proximal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_middle_intermediate">
      <inertial>
        <origin
          xyz="0.001921 0.020796 -0.005"
          rpy="0 0 0" />
        <mass
          value="0.0050396" />
        <inertia
          ixx="9.5822E-07"
          ixy="1.1425E-07"
          ixz="-2.4791E-12"
          iyy="1.0646E-07"
          iyz="5.9173E-12"
          izz="9.8384E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_middle_intermediate_joint"
      type="revolute">
      <origin
        xyz="-0.0024229 0.032041 -0.001"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_middle_proximal" />
      <child
        link="${prefix}R_middle_intermediate" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_middle_proximal_joint"
        multiplier="1"
        offset="0" />
    </joint>

    <link name="${prefix}R_middle_tip">
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <sphere radius="0.005"/>
        </geometry>
        <material name="${prefix}mat_green">
          <color rgba="0 1 0 1" />
        </material>
      </visual>
    </link>

    <joint name="${prefix}R_middle_tip_joint" type="fixed">
      <parent link="${prefix}R_middle_intermediate"/>
      <child link="${prefix}R_middle_tip"/>
      <origin rpy="0 0 0" xyz="-0.005 0.045 -0.004"/>
    </joint>

    <!-- Ring Finger -->
    <link name="${prefix}R_ring_proximal">
      <inertial>
        <origin
          xyz="0.001297 0.011934 -0.0060002"
          rpy="0 0 0" />
        <mass
          value="0.0042403" />
        <inertia
          ixx="6.6211E-07"
          ixy="-1.8461E-08"
          ixz="1.5793E-12"
          iyy="2.1167E-07"
          iyz="-6.6868E-12"
          izz="6.9397E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_ring_proximal_joint"
      type="revolute">
      <origin
        xyz="0.00028533 -0.13691 0.0062872"
        rpy="3.0892 0 0" />
      <parent
        link="${prefix}R_hand_base_link" />
      <child
        link="${prefix}R_ring_proximal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_ring_intermediate">
      <inertial>
        <origin
          xyz="0.0021753 0.019567 -0.005"
          rpy="0 0 0" />
        <mass
          value="0.0045683" />
        <inertia
          ixx="7.6286E-07"
          ixy="8.0635E-08"
          ixz="-6.1562E-13"
          iyy="9.431E-08"
          iyz="5.8619E-13"
          izz="7.8177E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_ring_intermediate_joint"
      type="revolute">
      <origin
        xyz="-0.0024229 0.032041 -0.001"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_ring_proximal" />
      <child
        link="${prefix}R_ring_intermediate" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_ring_proximal_joint"
        multiplier="1"
        offset="0" />
    </joint>

    <link name="${prefix}R_ring_tip">
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <sphere radius="0.005"/>
        </geometry>
        <material name="${prefix}mat_green">
          <color rgba="0 1 0 1" />
        </material>
      </visual>
    </link>

    <joint name="${prefix}R_ring_tip_joint" type="fixed">
      <parent link="${prefix}R_ring_intermediate"/>
      <child link="${prefix}R_ring_tip"/>
      <origin rpy="0 0 0" xyz="-0.002 0.04 -0.004"/>
    </joint>

    <!-- Pinky Finger -->
    <link name="${prefix}R_pinky_proximal">
      <inertial>
        <origin
          xyz="0.001297 0.011934 -0.0060001"
          rpy="0 0 0" />
        <mass
          value="0.0042403" />
        <inertia
          ixx="6.6211E-07"
          ixy="-1.8461E-08"
          ixz="1.6907E-12"
          iyy="2.1167E-07"
          iyz="-6.9334E-12"
          izz="6.9397E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_pinky_proximal_joint"
      type="revolute">
      <origin
        xyz="0.00028533 -0.13571 0.025488"
        rpy="3.0369 0 0" />
      <parent
        link="${prefix}R_hand_base_link" />
      <child
        link="${prefix}R_pinky_proximal" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
    </joint>

    <link name="${prefix}R_pinky_intermediate">
      <inertial>
        <origin
          xyz="0.0024748 0.016203 -0.0050031"
          rpy="0 0 0" />
        <mass
          value="0.0035996" />
        <inertia
          ixx="4.3913E-07"
          ixy="4.1418E-08"
          ixz="3.7168E-11"
          iyy="7.0247E-08"
          iyz="5.8613E-11"
          izz="4.4867E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_R.STL" />
        </geometry>
        <material name="${prefix}mat_grey" />
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_R.STL" />
        </geometry>
      </collision>
    </link>

    <joint
      name="${prefix}R_pinky_intermediate_joint"
      type="revolute">
      <origin
        xyz="-0.0024229 0.032041 -0.001"
        rpy="0 0 0" />
      <parent
        link="${prefix}R_pinky_proximal" />
      <child
        link="${prefix}R_pinky_intermediate" />
      <axis
        xyz="0 0 1" />
      <limit
        lower="0"
        upper="1.7"
        effort="1"
        velocity="0.5" />
      <mimic
        joint="${prefix}R_pinky_proximal_joint"
        multiplier="1"
        offset="0" />
    </joint>

    <link name="${prefix}R_pinky_tip">
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <sphere radius="0.005"/>
        </geometry>
        <material name="${prefix}mat_green">
          <color rgba="0 1 0 1" />
        </material>
      </visual>
    </link>

    <joint name="${prefix}R_pinky_tip_joint" type="fixed">
      <parent link="${prefix}R_pinky_intermediate"/>
      <child link="${prefix}R_pinky_tip"/>
      <origin rpy="0 0 0" xyz="-0.002 0.032 -0.004"/>
    </joint>

  </xacro:macro>
</robot> 