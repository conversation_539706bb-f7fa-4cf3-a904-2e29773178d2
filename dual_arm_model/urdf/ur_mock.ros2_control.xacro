<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:macro name="ur_arm_ros2_control" params="prefix command_topic">
    <xacro:property name="joint_cmd_topic" value="/${command_topic}" />
    <ros2_control name="${prefix}ur_arm_system" type="system">
      <hardware>
          <plugin>mock_components/GenericSystem</plugin>
          <param name="mock_sensor_commands">true</param>
          <param name="state_following_offset">0.0</param>
          <param name="calculate_dynamics">true</param>
          <param name="joints">
            [${prefix}shoulder_pan_joint, ${prefix}shoulder_lift_joint, ${prefix}elbow_joint,
            ${prefix}wrist_1_joint, ${prefix}wrist_2_joint, ${prefix}wrist_3_joint]
          </param>
          <param name="command_interfaces">[position]</param>
          <param name="state_interfaces">[position, velocity]</param>
      </hardware>
      <!-- Each joint definition -->
      <joint name="${prefix}shoulder_pan_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}shoulder_lift_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}elbow_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_1_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_2_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>

      <joint name="${prefix}wrist_3_joint">
        <command_interface name="position"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
      </joint>
    </ros2_control>
  </xacro:macro>

</robot>