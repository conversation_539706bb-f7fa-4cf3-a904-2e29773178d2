<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:include filename="$(find dual_arm_model)/urdf/inc/ur_common.xacro" />

    <xacro:macro name="ur_robot" params="
    tf_prefix
    joint_limits_parameters_file
    kinematics_parameters_file
    physical_parameters_file
    visual_parameters_file
    safety_limits:=false
    safety_pos_margin:=0.15
    safety_k_position:=20"
    >

      <!-- Load configuration data from the provided .yaml files -->
    <xacro:read_model_data
      joint_limits_parameters_file="${joint_limits_parameters_file}"
      kinematics_parameters_file="${kinematics_parameters_file}"
      physical_parameters_file="${physical_parameters_file}"
      visual_parameters_file="${visual_parameters_file}"
      force_abs_paths="false"/>

    <!-- Add URDF transmission elements (for ros_control) -->
    <!--<xacro:ur_arm_transmission prefix="${prefix}" hw_interface="${transmission_hw_interface}" />-->
    <!-- Placeholder for ros2_control transmission which don't yet exist -->

    <!-- links -  main serial chain -->
    <link name="${tf_prefix}base_link"/>
    <link name="${tf_prefix}base_link_inertia">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="base" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="base" type="collision"/>
        </geometry>
      </collision>
      <xacro:cylinder_inertial radius="${base_inertia_radius}" length="${base_inertia_length}" mass="${base_mass}">
        <origin xyz="0 0 0" rpy="0 0 0" />
      </xacro:cylinder_inertial>
    </link>
    <link name="${tf_prefix}shoulder_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="shoulder" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
        <geometry>
          <xacro:get_mesh name="shoulder" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${shoulder_mass}"/>
        <origin rpy="${shoulder_inertia_rotation}" xyz="${shoulder_cog}"/>
        <inertia
            ixx="${shoulder_inertia_ixx}"
            ixy="${shoulder_inertia_ixy}"
            ixz="${shoulder_inertia_ixz}"
            iyy="${shoulder_inertia_iyy}"
            iyz="${shoulder_inertia_iyz}"
            izz="${shoulder_inertia_izz}"
        />
      </inertial>    </link>
    <link name="${tf_prefix}upper_arm_link">
      <visual>
        <origin xyz="0 0 ${shoulder_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="upper_arm" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${shoulder_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="upper_arm" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${upper_arm_mass}"/>
        <origin rpy="${upper_arm_inertia_rotation}" xyz="${upper_arm_cog}"/>
        <inertia
            ixx="${upper_arm_inertia_ixx}"
            ixy="${upper_arm_inertia_ixy}"
            ixz="${upper_arm_inertia_ixz}"
            iyy="${upper_arm_inertia_iyy}"
            iyz="${upper_arm_inertia_iyz}"
            izz="${upper_arm_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}forearm_link">
      <visual>
        <origin xyz="0 0 ${elbow_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="forearm" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${elbow_offset}" rpy="${pi/2} 0 ${-pi/2}"/>
        <geometry>
          <xacro:get_mesh name="forearm" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${forearm_mass}"/>
        <origin rpy="${forearm_inertia_rotation}" xyz="${forearm_cog}"/>
        <inertia
            ixx="${forearm_inertia_ixx}"
            ixy="${forearm_inertia_ixy}"
            ixz="${forearm_inertia_ixz}"
            iyy="${forearm_inertia_iyy}"
            iyz="${forearm_inertia_iyz}"
            izz="${forearm_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_1_link">
	  <xacro:get_visual_params name="wrist_1" type="visual_offset"/>
      <visual>
      	<origin xyz="0 0 ${visual_params}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_1" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${visual_params}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_1" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_1_mass}"/>
        <origin rpy="${wrist_1_inertia_rotation}" xyz="${wrist_1_cog}"/>
        <inertia
            ixx="${wrist_1_inertia_ixx}"
            ixy="${wrist_1_inertia_ixy}"
            ixz="${wrist_1_inertia_ixz}"
            iyy="${wrist_1_inertia_iyy}"
            iyz="${wrist_1_inertia_iyz}"
            izz="${wrist_1_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_2_link">
          <xacro:get_visual_params name="wrist_2" type="visual_offset"/>
      <visual>
        <origin xyz="0 0 ${visual_params}" rpy="0 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_2" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="0 0 ${visual_params}" rpy="0 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_2" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_2_mass}"/>
        <origin rpy="${wrist_2_inertia_rotation}" xyz="${wrist_2_cog}"/>
        <inertia
            ixx="${wrist_2_inertia_ixx}"
            ixy="${wrist_2_inertia_ixy}"
            ixz="${wrist_2_inertia_ixz}"
            iyy="${wrist_2_inertia_iyy}"
            iyz="${wrist_2_inertia_iyz}"
            izz="${wrist_2_inertia_izz}"
        />
      </inertial>
    </link>
    <link name="${tf_prefix}wrist_3_link">
      <!-- TODO: remove this once all wrist_3 meshes have the visual_offset_xyz tag -->
      <xacro:get_visual_params name="wrist_3" type="visual_offset_xyz"/>
      <xacro:property name="mesh_offset" value="${visual_params}" scope="parent"/>

      <xacro:if value="${visual_params == ''}">
        <xacro:get_visual_params name="wrist_3" type="visual_offset"/>
        <xacro:property name="mesh_offset" value="0 0 ${visual_params}" scope="parent"/>
      </xacro:if>
      <visual>
        <origin xyz="${mesh_offset}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_3" type="visual"/>
        </geometry>
      </visual>
      <collision>
        <origin xyz="${mesh_offset}" rpy="${pi/2} 0 0"/>
        <geometry>
          <xacro:get_mesh name="wrist_3" type="collision"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="${wrist_3_mass}"/>
        <origin rpy="${wrist_3_inertia_rotation}" xyz="${wrist_3_cog}"/>
        <inertia
            ixx="${wrist_3_inertia_ixx}"
            ixy="${wrist_3_inertia_ixy}"
            ixz="${wrist_3_inertia_ixz}"
            iyy="${wrist_3_inertia_iyy}"
            iyz="${wrist_3_inertia_iyz}"
            izz="${wrist_3_inertia_izz}"
        />
      </inertial>
    </link>

    <!-- base_joint fixes base_link to the environment -->
    <!-- <joint name="${tf_prefix}base_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent}" />
      <child link="${tf_prefix}base_link" />
    </joint> -->

    <!-- joints - main serial chain -->
    <joint name="${tf_prefix}base_link-base_link_inertia" type="fixed">
      <parent link="${tf_prefix}base_link" />
      <child link="${tf_prefix}base_link_inertia" />
      <!-- 'base_link' is REP-103 aligned (so X+ forward), while the internal
           frames of the robot/controller have X+ pointing backwards.
           Use the joint between 'base_link' and 'base_link_inertia' (a dummy
           link/frame) to introduce the necessary rotation over Z (of pi rad).
      -->
      <origin xyz="0 0 0" rpy="0 0 ${pi}" />
    </joint>
    <joint name="${tf_prefix}shoulder_pan_joint" type="revolute">
      <parent link="${tf_prefix}base_link_inertia" />
      <child link="${tf_prefix}shoulder_link" />
      <origin xyz="${shoulder_x} ${shoulder_y} ${shoulder_z}" rpy="${shoulder_roll} ${shoulder_pitch} ${shoulder_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${shoulder_pan_lower_limit}" upper="${shoulder_pan_upper_limit}"
        effort="${shoulder_pan_effort_limit}" velocity="${shoulder_pan_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${shoulder_pan_lower_limit + safety_pos_margin}" soft_upper_limit="${shoulder_pan_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}shoulder_lift_joint" type="revolute">
      <parent link="${tf_prefix}shoulder_link" />
      <child link="${tf_prefix}upper_arm_link" />
      <origin xyz="${upper_arm_x} ${upper_arm_y} ${upper_arm_z}" rpy="${upper_arm_roll} ${upper_arm_pitch} ${upper_arm_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${shoulder_lift_lower_limit}" upper="${shoulder_lift_upper_limit}"
        effort="${shoulder_lift_effort_limit}" velocity="${shoulder_lift_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${shoulder_lift_lower_limit + safety_pos_margin}" soft_upper_limit="${shoulder_lift_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}elbow_joint" type="revolute">
      <parent link="${tf_prefix}upper_arm_link" />
      <child link="${tf_prefix}forearm_link" />
      <origin xyz="${forearm_x} ${forearm_y} ${forearm_z}" rpy="${forearm_roll} ${forearm_pitch} ${forearm_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${elbow_joint_lower_limit}" upper="${elbow_joint_upper_limit}"
        effort="${elbow_joint_effort_limit}" velocity="${elbow_joint_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${elbow_joint_lower_limit + safety_pos_margin}" soft_upper_limit="${elbow_joint_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_1_joint" type="revolute">
      <parent link="${tf_prefix}forearm_link" />
      <child link="${tf_prefix}wrist_1_link" />
      <origin xyz="${wrist_1_x} ${wrist_1_y} ${wrist_1_z}" rpy="${wrist_1_roll} ${wrist_1_pitch} ${wrist_1_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${wrist_1_lower_limit}" upper="${wrist_1_upper_limit}"
        effort="${wrist_1_effort_limit}" velocity="${wrist_1_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${wrist_1_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_1_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_2_joint" type="revolute">
      <parent link="${tf_prefix}wrist_1_link" />
      <child link="${tf_prefix}wrist_2_link" />
      <origin xyz="${wrist_2_x} ${wrist_2_y} ${wrist_2_z}" rpy="${wrist_2_roll} ${wrist_2_pitch} ${wrist_2_yaw}" />
      <axis xyz="0 0 1" />
      <limit lower="${wrist_2_lower_limit}" upper="${wrist_2_upper_limit}"
             effort="${wrist_2_effort_limit}" velocity="${wrist_2_velocity_limit}"/>
      <xacro:if value="${safety_limits}">
         <safety_controller soft_lower_limit="${wrist_2_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_2_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
      </xacro:if>
      <dynamics damping="0" friction="0"/>
    </joint>
    <joint name="${tf_prefix}wrist_3_joint" type="${wrist_3_joint_type}">
      <parent link="${tf_prefix}wrist_2_link" />
      <child link="${tf_prefix}wrist_3_link" />
      <origin xyz="${wrist_3_x} ${wrist_3_y} ${wrist_3_z}" rpy="${wrist_3_roll} ${wrist_3_pitch} ${wrist_3_yaw}" />
      <axis xyz="0 0 1" />
      <xacro:if value="${wrist_3_joint_type != 'continuous'}">
        <limit lower="${wrist_3_lower_limit}" upper="${wrist_3_upper_limit}"
          effort="${wrist_3_effort_limit}" velocity="${wrist_3_velocity_limit}"/>
        <xacro:if value="${safety_limits}">
          <safety_controller soft_lower_limit="${wrist_3_lower_limit + safety_pos_margin}" soft_upper_limit="${wrist_3_upper_limit - safety_pos_margin}" k_position="${safety_k_position}" k_velocity="0.0"/>
        </xacro:if>
      </xacro:if>
      <xacro:unless value="${wrist_3_joint_type != 'continuous'}">
        <limit effort="${wrist_3_effort_limit}" velocity="${wrist_3_velocity_limit}"/>
        <xacro:if value="${safety_limits}">
          <safety_controller k_position="${safety_k_position}" k_velocity="0.0"/>
        </xacro:if>
      </xacro:unless>
      <dynamics damping="0" friction="0"/>
    </joint>

    <link name="${tf_prefix}ft_frame"/>
    <joint name="${tf_prefix}wrist_3_link-ft_frame" type="fixed">
      <parent link="${tf_prefix}wrist_3_link"/>
      <child link="${tf_prefix}ft_frame"/>
      <origin xyz="0 0 0" rpy="${pi} 0 0"/>
    </joint>

    <!-- ROS-Industrial 'base' frame - base_link to UR 'Base' Coordinates transform -->
    <link name="${tf_prefix}base"/>
    <joint name="${tf_prefix}base_link-base_fixed_joint" type="fixed">
      <!-- Note the rotation over Z of pi radians - as base_link is REP-103
           aligned (i.e., has X+ forward, Y+ left and Z+ up), this is needed
           to correctly align 'base' with the 'Base' coordinate system of
           the UR controller.
      -->
      <origin xyz="0 0 0" rpy="0 0 ${pi}"/>
      <parent link="${tf_prefix}base_link"/>
      <child link="${tf_prefix}base"/>
    </joint>

    <!-- ROS-Industrial 'flange' frame - attachment point for EEF models -->
    <link name="${tf_prefix}flange" />
    <joint name="${tf_prefix}wrist_3-flange" type="fixed">
      <parent link="${tf_prefix}wrist_3_link" />
      <child link="${tf_prefix}flange" />
      <origin xyz="0 0 0" rpy="0 ${-pi/2.0} ${-pi/2.0}" />
    </joint>

    <!-- ROS-Industrial 'tool0' frame - all-zeros tool frame -->
    <link name="${tf_prefix}tool0"/>
    <joint name="${tf_prefix}flange-tool0" type="fixed">
      <!-- default toolframe - X+ left, Y+ up, Z+ front -->
      <origin xyz="0 0 0" rpy="${pi/2.0} 0 ${pi/2.0}"/>
      <parent link="${tf_prefix}flange"/>
      <child link="${tf_prefix}tool0"/>
    </joint>

  </xacro:macro>

</robot>